{
  "easycom": {
    "custom": {
      "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue",
      "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"
    }
  },
  "pages": [
    {
      "path": "pages/tab/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/views/course/detail",
      "style": {
        "navigationBarTitleText": "课程详情"
      }
    },
    {
      "path": "pages/views/lessonPlan/detail",
      "style": {
        "navigationBarTitleText": "教案详情"
      }
    },
    {
      "path": "pages/views/application/detail",
      "style": {
        "navigationBarTitleText": "跨学科应用"
      }
    },
    {
      "path": "pages/views/task/detail",
      "style": {
        "navigationBarTitleText": "课后发散"
      }
    },
    {
      "path": "pages/views/userList/index",
      "style": {
        "navigationBarTitleText": "管理成员"
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/common",
      "pages": [
        {
          "path": "webview/index",
          "navigationBarTitleText": "网页"
        }
      ]
    }
  ],
  "preloadRule": {
    "pages/tab/home/<USER>": {
      "network": "all",
      "packages": ["pages/common"]
    }
  },
  // "tabBar": {
  //   "color": "#666666",
  //   "selectedColor": "#4AAAF8",
  //   "fontSize": "10px",
  //   "iconWidth": "24px",
  //   "borderStyle": "black",
  //   "backgroundColor": "#ffffff",
  //   "list": [{
  //     "pagePath": "pages/tab/home/<USER>",
  //     "iconPath": "static/images/tabIcon/home.png",
  //     "selectedIconPath": "static/images/tabIcon/home_select.png",
  //     "text": "首页"
  //   }, {
  //     "pagePath": "pages/tab/curriculum/index",
  //     "iconPath": "static/images/tabIcon/read.png",
  //     "selectedIconPath": "static/images/tabIcon/read_select.png",
  //     "text": "阅读"
  //   }]
  // },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#FFF",
    "backgroundColor": "#FFF"
  },
  "condition": { // 模式配置，仅开发期间生效
    "current": 0, // 当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", // 模式名称
        "path": "pages/views/answer/end", // 启动页面，必选
        "query": "" // 启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
