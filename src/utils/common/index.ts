// 小程序更新检测
export function mpUpdate() {
  const updateManager = uni.getUpdateManager();
  updateManager.onCheckForUpdate((res) => {
    // 请求完新版本信息的回调
    console.log(res.hasUpdate);
  });
  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '检测到新版本，是否下载新版本并重启小程序？',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        }
      },
    });
  });
  updateManager.onUpdateFailed(() => {
    // 新的版本下载失败
    uni.showModal({
      title: '已经有新版本了哟~',
      content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~',
      showCancel: false,
    });
  });
}

export function formatPrice(price: string | number) {
  if (price === null || price === undefined)
    return '';
  return String(price).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 将时间戳转换为指定格式的字符串
 * @param timestamp - 时间戳（毫秒）
 * @param format - 时间格式，支持 'yyyy-MM-DD' 和 'yyyy-MM-DD hh:mm:ss'，默认为 'yyyy-MM-DD hh:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function timestampToString(timestamp: string, format: 'yyyy-MM-DD' | 'yyyy-MM-DD hh:mm:ss' = 'yyyy-MM-DD hh:mm:ss'): string {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  if (format === 'yyyy-MM-DD') {
    return `${year}-${month}-${day}`;
  }
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
