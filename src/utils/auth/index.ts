const TokenKey = 'admin-token';
const TokenPrefix = 'Bearer ';
function isLogin() {
  return !!uni.getStorageSync(TokenKey);
}
function getToken() {
  return uni.getStorageSync(TokenKey);
}
function getRefreshToken() {
  return uni.getStorageSync('refresh-token');
}
// 设置 token
function setToken(token = '', refreshToken = '') {
  uni.setStorageSync(TokenKey, token);
  uni.setStorageSync('refresh-token', refreshToken);
}
function clearToken() {
  uni.removeStorageSync(TokenKey);
  uni.removeStorageSync('refresh-token');
}

function setLoginRecord() {
  uni.setStorageSync('login-record', new Date().getTime());
}

function getLoginRecord() {
  return uni.getStorageSync('login-record');
}
export { TokenPrefix, isLogin, getToken, setToken, clearToken, getRefreshToken, setLoginRecord, getLoginRecord };
