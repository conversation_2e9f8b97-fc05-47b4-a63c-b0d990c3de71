<template>
  <view class="test-page">
    <view class="header">
      <text class="title">登录状态测试页面</text>
    </view>
    
    <view class="status-section">
      <text class="section-title">当前登录状态</text>
      <view class="status-display">
        <text :class="['status-text', loginFlag ? 'logged-in' : 'logged-out']">
          {{ loginFlag ? '已登录' : '未登录' }}
        </text>
      </view>
    </view>
    
    <view class="info-section">
      <text class="section-title">用户信息</text>
      <view class="info-display">
        <text class="info-item">ID: {{ userStore.id || '未设置' }}</text>
        <text class="info-item">昵称: {{ userStore.nickname || '未设置' }}</text>
        <text class="info-item">手机号: {{ userStore.mobile || '未设置' }}</text>
      </view>
    </view>
    
    <view class="actions-section">
      <text class="section-title">操作</text>
      <view class="actions">
        <button 
          v-if="!loginFlag" 
          class="action-btn login-btn" 
          open-type="getPhoneNumber" 
          @getphonenumber="handleLogin"
        >
          微信手机号登录
        </button>
        <button 
          v-else 
          class="action-btn logout-btn" 
          @click="handleLogout"
        >
          退出登录
        </button>
        <button class="action-btn refresh-btn" @click="refreshStatus">
          刷新状态
        </button>
      </view>
    </view>
    
    <view class="debug-section">
      <text class="section-title">调试信息</text>
      <view class="debug-info">
        <text class="debug-item">Store isLoggedIn: {{ userStore.isLoggedIn }}</text>
        <text class="debug-item">Store loginFlag: {{ userStore.loginFlag }}</text>
        <text class="debug-item">Local Token: {{ hasLocalToken ? '存在' : '不存在' }}</text>
        <text class="debug-item">最后更新: {{ lastUpdate }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store';
import { getToken } from '@/utils/auth';

const userStore = useUserStore();

// 响应式登录状态
const loginFlag = computed(() => userStore.loginFlag);

// 本地 token 状态
const hasLocalToken = computed(() => !!getToken());

// 最后更新时间
const lastUpdate = ref(new Date().toLocaleTimeString());

// 处理登录
const handleLogin = async (e: any) => {
  try {
    const result = await userStore.mobileLogin(e.detail);
    if (result) {
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });
      lastUpdate.value = new Date().toLocaleTimeString();
    } else {
      uni.showToast({
        title: '登录失败',
        icon: 'error'
      });
    }
  } catch (error) {
    console.error('登录错误:', error);
    uni.showToast({
      title: '登录出错',
      icon: 'error'
    });
  }
};

// 处理登出
const handleLogout = async () => {
  try {
    await userStore.logout();
    uni.showToast({
      title: '已退出登录',
      icon: 'success'
    });
    lastUpdate.value = new Date().toLocaleTimeString();
  } catch (error) {
    console.error('登出错误:', error);
    uni.showToast({
      title: '登出出错',
      icon: 'error'
    });
  }
};

// 刷新状态
const refreshStatus = () => {
  userStore.checkAndUpdateLoginStatus();
  lastUpdate.value = new Date().toLocaleTimeString();
  uni.showToast({
    title: '状态已刷新',
    icon: 'success'
  });
};

// 页面显示时检查状态
onShow(() => {
  userStore.checkAndUpdateLoginStatus();
  lastUpdate.value = new Date().toLocaleTimeString();
});
</script>

<style scoped>
.test-page {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status-section,
.info-section,
.actions-section,
.debug-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.status-display {
  text-align: center;
  padding: 20rpx;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
}

.logged-in {
  color: #52c41a;
  background-color: #f6ffed;
  border: 2rpx solid #b7eb8f;
}

.logged-out {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 2rpx solid #ffccc7;
}

.info-display,
.debug-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item,
.debug-item {
  font-size: 24rpx;
  color: #666;
  padding: 12rpx;
  background-color: #fafafa;
  border-radius: 8rpx;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.action-btn {
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.login-btn {
  background-color: #1890ff;
  color: white;
}

.logout-btn {
  background-color: #ff4d4f;
  color: white;
}

.refresh-btn {
  background-color: #52c41a;
  color: white;
}

.action-btn:active {
  opacity: 0.8;
}
</style>
