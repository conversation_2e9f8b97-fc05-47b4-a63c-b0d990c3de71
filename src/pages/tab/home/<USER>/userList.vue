<template>
  <up-popup :show="show" mode="bottom" @close="close" @open="open">
    <view class="user-list">
      <view class="user-title">
        <text class="title-text">
          团队成员
        </text>
        <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_close_2.png" @click="close" />
      </view>

      <view class="user-list">
        <view v-for="item in userList" :key="item.memberId" class="list-item">
          <image class="item-img" :src="item.memberAvatar" />
          <text class="item-name">
            {{ item.memberNickname }}
          </text>
          <view v-if="showDel && chooseId === item.memberId" class="item-btn" @click="delItem(item)">
            移除成员
          </view>
          <up-icon v-if="showManage && item.adminMember === 0" name="more-dot-fill" @click="handleClick(item.memberId)" />
        </view>
      </view>

      <button class="user-btn" open-type="share" hover-class="none">
        邀请老师进入课程
      </button>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
import type { MemberInfo } from '@/api/user/types';
import { invite, removeMember } from '@/api/user';
import { useUserStore } from '@/store';
import type { StageList } from '@/api/course/types';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },

  userList: {
    type: Array as PropType<MemberInfo[]>,
    default: () => [],
  },

  stage: {
    type: Object as PropType<StageList>,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:show', 'updateList']);
const userStore = useUserStore();
const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};

const showDel = ref(false);
const chooseId = ref<number>();
const handleClick = (id: number) => {
  if (chooseId.value === id) {
    showDel.value = !showDel.value;
  }
  else {
    showDel.value = true;
  }
  chooseId.value = id;
};
// 删除成员
const delItem = (item: any) => {
  removeMember(item.memberId).then(() => {
    uni.showToast({
      title: '删除成功',
    });
    emit('updateList');
  });
};

const showManage = computed(() => {
  return props.userList.some((item: MemberInfo) => item.adminMember === 1 && item.memberId === userStore.id);
});

// 分享
uni.showShareMenu({
  withShareTicket: true,
  menus: ['shareAppMessage', 'shareTimeline'],
});
onShareAppMessage(async () => {
  // TODO: 邀请达上限
  const code = await invite({ stageIds: [props.stage.id] });
  const params = {
    userName: userStore.nickname,
    userAvatar: userStore.avatar,
    stageName: props.stage.name,
    code: code.invitationCode,
  };
  return {
    title: '邀请你共享',
    path: `/pages/tab/home/<USER>
    success: () => {
      uni.showToast({
        title: '分享成功',
      });
    },
    fail: () => {
      uni.showToast({
        title: '分享失败',
      });
    },
  };
});
</script>

<style lang="scss" scoped>
.user-list {
  width: 750rpx;
  padding: 32rpx;

  display: flex;
  flex-direction: column;
  align-items: center;

  .user-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-text {
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }

    .title-img {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .user-list {
    width: 100%;
    padding: 32rpx 0 20rpx 0;
    border-bottom: 2rpx solid #F5F6F9;;

    .list-item {
      width: 100%;
      height: 112rpx;
      padding: 0 16rpx 8rpx 16rpx;
      display: flex;
      align-items: center;

      .item-img {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
      }

      .item-name {
        flex: 1;
        padding-left: 12rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: #333;
      }

      .item-btn {
        margin-right: 26rpx;
        padding: 0 32rpx;
        background: #FFFFFF;
        box-shadow: 0 8rpx 20rpx 0 rgba(0,0,0,0.1);
        border-radius: 8rpx;

        font-size: 28rpx;
        color: #303030;
        line-height: 72rpx;
      }
    }
  }

  .user-btn {
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    width: 686rpx;
    height: 104rpx;
    background: #7151ED;
    border-radius: 16rpx;

    font-size: 32rpx;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
    line-height: 104rpx;
  }
}
</style>

<style lang="scss">
user-list {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // background: transparent !important;
    background: #fff;
  }

  .u-icon__icon {
    font-size: 32rpx !important;
    color: #333333 !important;
  }
}
</style>
