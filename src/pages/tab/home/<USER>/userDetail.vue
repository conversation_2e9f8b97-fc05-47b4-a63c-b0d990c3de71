<template>
  <up-popup :show="show" mode="left" @close="close" @open="open">
    <view class="user-detail">
      <view class="user-header" @click="editInfoShow = true">
        <image class="user-img" :src="userStore.avatar || 'https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/user_img.png'" />
        <text class="user-name">
          {{ displayName }}
        </text>
      </view>

      <view class="user-level">
        <view class="level-content" :class="[(!isVip || isExpire) && 'user-no-vip']">
          <view class="level-top">
            <text class="level-text">
              {{ isVip ? '会员等级' : '开通会员' }}
            </text>
            <text v-if="isVip" class="level-date">
              {{ isExpire ? '过期日期：' : '有效期：' }}{{ timestampToString(userStore.levelExpireTime, 'yyyy-MM-DD') }}
            </text>
          </view>
          <view class="level-num">
            {{ isVip ? `${userStore.courseCount}节课程 ${userStore.unitCount}项课程` : '开通会员后可享受100+课程' }}
          </view>
        </view>
        <view class="level-info" @click="vipShow = true">
          {{ isVip ? '升级高级会员，可享孩子个性化反馈 〉' : '升级会员，享受更多课程、更多成员数量 〉' }}
        </view>
      </view>

      <view class="user-group">
        <view class="group-title">
          <text class="title-text">
            团队成员
          </text>
          <text v-if="showManage" class="title-btn" @click="toUserList">
            管理
          </text>
        </view>

        <view class="group-list">
          <template v-if="isVip">
            <template v-for="(item, index) in memberList">
              <view v-if="index < 3" :key="item.memberId" class="list-item">
                <image class="item-img" :src="item.memberAvatar || 'https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/user_img.png'" />
                <text class="item-name">
                  {{ item.memberNickname }}
                </text>

                <!-- TODO: -->
                <image v-if="item.adminMember === 1" class="item-admin" src="" />
              </view>
            </template>
          </template>

          <template v-else>
            <view class="list-item">
              <image class="item-img" :src="userStore.avatar || 'https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/user_img.png'" />
              <text class="item-name">
                {{ displayName }}
              </text>
            </view>
            <view v-for="item in 2" :key="`no${item}`" class="list-item">
              <image class="item-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/user_img.png" />
              <text class="item-name">
                待加入
              </text>
            </view>
          </template>
        </view>
      </view>

      <view class="user-course">
        <view class="course-title">
          我的课程
        </view>
        <view class="course-list">
          <view v-for="item in courseList" :key="item.id" class="list-item">
            <text class="item-text">
              {{ item.name }}
            </text>
            <text v-if="item.owned && !isExpire" class="item-text">
              {{ item.courseCount }}
            </text>
            <text v-else class="item-text item-btn" @click="handleCourse(item)">
              开通 >
            </text>
          </view>
        </view>
      </view>

      <view class="user-logout" @click="logout">
        退出登录
      </view>
    </view>
  </up-popup>
  <vipBox v-model:show="vipShow" :title="vipTitle" :btn-text="vipBtnText" @update-data="getData(true)" />
  <editInfo v-model:show="editInfoShow" />
  <!-- <ConfirmBox v-model:show="confirmShow" title="您的会员已过期！请联系客服继续开通会员。" btn-text="继续开通会员" @handle-confirm="handleConfirm" /> -->
</template>

<script setup lang="ts">
import vipBox from './vipBox.vue';
import editInfo from './editInfo.vue';
import { useUserStore } from '@/store';
import { getMembers, myCourses } from '@/api/user';
import type { CourseInfo, MemberInfo } from '@/api/user/types';
import { timestampToString } from '@/utils/common';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:show', 'logout', 'updateData']);
const userStore = useUserStore();
const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};

const isVip = computed(() => {
  return !!userStore.level;
});

// 是否vip过期
const isExpire = computed(() => {
  return new Date().getTime() > Number(userStore.levelExpireTime);
});

// 用户显示名称（昵称优先，否则显示脱敏手机号）
const displayName = computed(() => userStore.displayName);

const logout = () => {
  // 退出登录
  userStore.logout();
  close();
  emit('logout');
};

const vipShow = ref(false);
const vipTitle = ref('正课需要开通会员才能看哦！');
const vipBtnText = ref('兑换会员');
// const confirmShow = ref(false); // vip过期提醒弹窗
// const convertShow = ref(false); // 会员兑换
// // vip过期后兑换会员
// const handleConfirm = () => {
//   confirmShow.value = false;
//   convertShow.value = true;
// };

// 编辑头像昵称
const editInfoShow = ref(false);
const courseList = ref<CourseInfo[]>([]);
const memberList = ref<MemberInfo[]>([]);
const getData = (flag: boolean) => {
  // 获取我的阶段列表
  myCourses().then(({ stages }: { stages: CourseInfo[] }) => {
    courseList.value = stages;
  });
  // 获取全部成员列表
  getMembers().then((res: MemberInfo[]) => {
    memberList.value = res;
  });
  if (flag) {
    emit('updateData');
  }
};

const showManage = computed(() => {
  return memberList.value.some((item: MemberInfo) => item.adminMember === 1 && item.memberId === userStore.id);
});

const handleCourse = (item?: CourseInfo) => {
  if (item ? !item.owned : !userStore.level) { // 会员课程 未购买
    vipShow.value = true;
    vipTitle.value = '正课需要开通会员才能看哦！';
    vipBtnText.value = '开通会员';
    return true;
  }
  if (isExpire.value) { // 会员过期
    vipShow.value = true;
    vipTitle.value = '您的会员已过期！';
    vipBtnText.value = '继续开通会员';
    return true;
  }
  return false;
};

const toUserList = () => {
  if (handleCourse())
    return;

  uni.navigateTo({
    url: '/pages/views/userList/index',
  });
};

watch(() => props.show, (newVal) => {
  if (newVal) {
    getData(false);
  }
});
</script>

<style lang="scss" scoped>
.user-detail {
  position: relative;
  background: #F8F8F8;
  height: 100%;
  padding: 120rpx 40rpx 40rpx 40rpx;
  overflow: auto;

  .user-header {
    display: flex;
    align-items: center;

    .user-img {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
    }

    .user-name {
      padding-left: 16rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #333333;
    }
  }

  .user-level {
    margin-top: 40rpx;
    background: #fff;
    border-radius: 16rpx;

    .level-content {
      padding: 24rpx;
      background: #54CE89;
      border-radius: 16rpx;
      color: #FFFFFF;

      .level-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .level-text {
          font-size: 32rpx;
          line-height: 48rpx;
          font-weight: bold;
        }

        .level-date {
          font-size: 22rpx;
          line-height: 33rpx;
        }
      }

      .level-num {
        padding-top: 40rpx;
        font-size: 26rpx;
        line-height: 40rpx;
      }
    }

    .user-no-vip {
      background: #EDFFF5;
      color: #333;
    }

    .level-info {
      padding: 16rpx 28rpx;
      font-size: 24rpx;
      color: #303030;
      line-height: 36rpx;
    }
  }

  .user-group {
    margin-top: 40rpx;
    padding: 24rpx 32rpx;
    background: #fff;
    border-radius: 16rpx;

    .group-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 24rpx;
      border-bottom: 2rpx dashed #DFE1E7;

      .title-text {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        line-height: 42rpx;
      }

      .title-btn {
        font-weight: 500;
        font-size: 28rpx;
        color: #7151ED;
        line-height: 42rpx;
      }
    }

    .group-list {
      padding-top: 12rpx;

      .list-item {
        position: relative;
        padding-top: 28rpx;
        display: flex;
        align-items: center;

        .item-img {
          width: 48rpx;
          height: 48rpx;
          border-radius: 50%;
        }

        .item-name {
          padding-left: 12rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 42rpx;
        }

        .item-admin {
          position: absolute;
          right: 0;
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
  }

  .user-course {
    margin-top: 40rpx;
    padding: 24rpx 32rpx;
    background: #fff;
    border-radius: 16rpx;

    .course-title {
      padding-bottom: 24rpx;
      border-bottom: 2rpx dashed #DFE1E7;

      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;
    }

    .course-list {
      padding-top: 12rpx;

      .list-item {
        padding-top: 28rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .item-text {
          font-weight: 500;
          font-size: 28rpx;
          color: #333333;
          line-height: 42rpx;
        }

        .item-btn {
          color: #7151ED;
        }
      }
    }
  }

  .user-logout {
    margin-top: 120rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: rgba(51,51,51,0.7);
    text-align: center;
    line-height: 42rpx;
  }
}
</style>

<style lang="scss">
user-detail {
  .u-popup__content {
    width: 600rpx;
  }
}
</style>
