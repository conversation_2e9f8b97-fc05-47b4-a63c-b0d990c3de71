<template>
  <up-popup :show="show" mode="center" :close-on-click-overlay="false" @close="close" @open="open">
    <view class="edit-info">
      <view class="info-title">
        个人信息
      </view>
      <button open-type="chooseAvatar" class="info-avatar" @chooseavatar="onChooseavatar">
        <image :src="userInfo.avatar" class="avatar-img" />
        <image class="avatar-icon" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_phone.png" />
      </button>
      <view class="info-nickname">
        <view class="nickname-label">
          昵称
        </view>
        <input
          type="nickname" class="nickname-input" :value="userInfo.nickname" placeholder="请输入昵称"
          @blur="bindblur" @input="bindinput"
        >
      </view>
      <view class="info-footer">
        <view class="footer-btn btn-margin" @click="close">
          取消
        </view>
        <view class="footer-btn" @click="save">
          保存
        </view>
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:show']);
const userStore = useUserStore();

const userInfo = ref({
  avatar: '',
  nickname: '',
});

const open = () => {
  emit('update:show', true);
  userInfo.value.avatar = userStore.avatar;
  userInfo.value.nickname = userStore.nickname;
};
const close = () => {
  emit('update:show', false);
};

// 获取头像
const onChooseavatar = async (e: any) => {
  const { avatarUrl } = e.detail;
  userInfo.value.avatar = avatarUrl;
  uni.showLoading({
    title: '加载中',
  });
  const res = await userStore.uploadAvatar(avatarUrl);
  userInfo.value.avatar = res;
};
const bindblur = (e: any) => {
  userInfo.value.nickname = e.detail.value; // 获取微信昵称
};
const bindinput = (e: any) => {
  userInfo.value.nickname = e.detail.value; // 这里要注意如果只用blur方法的话用户在输入玩昵称后直接点击保存按钮，会出现修改不成功的情况。
};

const save = async () => {
  await userStore.updateUserInfo(userInfo.value);
  close();
};
</script>

<style lang="scss" scoped>
.edit-info {
  width: 590rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;

  display: flex;
  flex-direction: column;
  align-items: center;

  .info-title {
    font-weight: bold;
    font-size: 32rpx;
    color: #303030;
    line-height: 48rpx;
  }

  .info-avatar {
    position: relative;
    margin-top: 40rpx;
    margin-bottom: 16rpx;
    width: 160rpx;
    height: 160rpx;
    background: transparent;
    border-radius: 0;

    &::after {
      border-color: transparent;
    }

    .avatar-img {
      width: 160rpx;
      height: 160rpx;
      border-radius: 50%;
      background: #D9D9D9;
    }

    .avatar-icon {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 64rpx;
      height: 64rpx;
    }
  }

  .info-nickname {
    width: 100%;

    .nickname-label {
      padding-bottom: 16rpx;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;
    }

    .nickname-input {
      padding: 20rpx;
      border-radius: 8rpx;
      border: 2rpx solid #C4C4C4;

      font-size: 28rpx;
      color: #64706E;
    }
  }

  .info-footer {
    width: 100%;
    padding-top: 60rpx;
    display: flex;
    justify-content: space-between;

    .footer-btn {
      padding: 0 83rpx;
      background: #7151ED;
      border: 2rpx solid #7151ED;
      border-radius: 8rpx;

      font-size: 32rpx;
      line-height: 80rpx;
      text-align: center;
      color: #FFFFFF;
    }

    .btn-margin {
      margin-right: 40rpx;
      background: #FFFFFF;
      color: #7151ED;
    }
  }
}

.close-btn {
  margin-top: 80rpx;
  width: 88rpx;
  height: 88rpx;
}
</style>

<style lang="scss">
edit-info {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }
}
</style>
