<template>
  <up-popup :show="show" mode="center" :close-on-click-overlay="false" @close="close" @open="open">
    <view class="invite-box">
      <view class="invite-title">
        <image class="user-img" :src="shareData.userAvatar" />
        <text class="user-name">
          {{ shareData.userName }}
        </text>
        <text class="user-info">
          邀请你共享
        </text>
      </view>
      <view class="invite-info">
        小牛墩墩 {{ shareData.stageName || '' }} 科学实验课程资源。
      </view>
      <button v-if="!loginFlag" class="invite-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
        接受邀请
      </button>
      <view v-else class="invite-btn" @click="handleInvite">
        接受邀请
      </view>
    </view>
    <image class="close-btn" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_close.png" @click="close" />
  </up-popup>
</template>

<script setup lang="ts">
import { accept } from '@/api/user';
import { isLogin } from '@/utils/auth';
import { useUserStore } from '@/store';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },

  shareData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:show', 'updateNumber']);
const userStore = useUserStore();
const loginFlag = computed(() => { // 是否登录
  return isLogin();
});
const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};
// TODO: 接受邀请
const handleInvite = () => {
  accept({ invitationCode: props.shareData.code }).then((res: any) => {
    if (res) {
      uni.showToast({
        title: '加入成功',
      });
      emit('updateNumber');
    }
  }).finally(() => {
    close();
  });
};

// 获取手机号登录
const getPhoneNumber = (e: any) => { // 在bindgetphonenumber回调中获取code动态令牌
  userStore.mobileLogin(e.detail).then((res) => {
    if (res) {
      emit('updateNumber');
      handleInvite();
    }
  });
};
</script>

<style lang="scss" scoped>
.invite-box {
  width: 590rpx;
  padding: 80rpx;
  background: #fff;
  border-radius: 40rpx;

  display: flex;
  flex-direction: column;
  align-items: center;

  .invite-title {
    width: 100%;
    display: flex;
    align-items: center;

    .user-img {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background: #D9D9D9;
    }

    .user-name {
      padding: 0 16rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }

    .user-info {
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }
  }

  .invite-info {
    padding: 24rpx 0;
    font-size: 32rpx;
    color: #303030;
    line-height: 72rpx;
  }

  .invite-btn {
    margin-top: 56rpx;
    width: 343rpx;
    height: 80rpx;
    background: #7151ED;
    border-radius: 16rpx;

    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 80rpx;
  }
}

.close-btn {
  margin-top: 80rpx;
  width: 88rpx;
  height: 88rpx;
}
</style>

<style lang="scss">
invite-box {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }
}
</style>
