<template>
  <up-popup :show="show" mode="center" :close-on-click-overlay="false" @close="close" @open="open">
    <view class="img-content">
      <view class="img-box">
        <image class="img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/course_icons/zs_long.png" />
      </view>
      <view class="img-footer">
        <view class="btn-left" @click="handleDownload">
          <image class="btn-icon" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/course_icons/download_icon.png" />
          <text class="btn-text">
            保存图片
          </text>
        </view>
        <view class="btn-right" @click="handleClick">
          查看课程
        </view>
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

defineProps({
  show: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:show', 'handleClick']);

const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};

const handleClick = () => {
  emit('update:show', false);
  emit('handleClick');
};

const handleDownload = () => {
  uni.downloadFile({
    url: 'https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/course_icons/zs_long.png',
    success: (res: any) => {
      const filePath = res.tempFilePath;
      uni.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success',
          });
        },
        fail: (err) => {
          console.error('保存失败', err);
          uni.showToast({
            title: '保存失败',
            icon: 'none',
          });
        },
      });
    },
    fail: () => {
      uni.hideLoading();
      uni.showToast({
        title: '下载失败',
        duration: 1500,
        icon: 'none',
      });
      console.log('下载失败');
    },
  });
};
</script>

<style lang="scss" scoped>
.img-content {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #fff;

  .img-box {
    width: 100%;
    height: 100%;
    overflow: auto;

    .img {
      width: 750rpx;
      height: 5934rpx;
    }
  }

  .img-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 32rpx 40rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -20rpx 20rpx 0 rgba(0,0,0,0.08);

    .btn-left {
      width: 200rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-right: 40rpx;

      .btn-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .btn-text {
        padding-top: 8rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 42rpx;
      }
    }

    .btn-right {
      flex: 1;
      background: #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 104rpx;
      text-align: center;
      color: #FFFFFF;
    }
  }
}
</style>

<style lang="scss">
vip-box {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }
}
</style>
