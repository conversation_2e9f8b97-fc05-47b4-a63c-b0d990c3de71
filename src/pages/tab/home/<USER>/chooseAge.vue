<template>
  <up-popup :show="show" mode="bottom" @close="close" @open="open">
    <view class="choose-age">
      <view class="age-title">
        <text class="title-text">
          {{ type === 1 ? '请选择你要的年龄阶段' : '请选择你要共享的阶段' }}
        </text>
        <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_close_2.png" @click="close" />
      </view>

      <view class="age-list">
        <view v-for="item in courseList" :key="item.id" class="list-item">
          <text class="item-text">
            {{ item.name }}
          </text>
          <text class="item-age">
            {{ item.description }}
          </text>
          <view class="item-checkbox" :class="[choosedIds.includes(item.id) && 'checked']" @click="handleChoose(item)">
            <up-icon v-if="choosedIds.includes(item.id)" name="checkbox-mark" />
          </view>
        </view>
      </view>

      <view v-if="type === 1" class="age-btn" @click="confirm">
        确定
      </view>
      <button v-else class="age-btn" open-type="share">
        分享给好友
      </button>
      <view v-if="type === 1" class="age-info" @click="showVip">
        升级会员 开通全年龄阶段课程
      </view>
    </view>
  </up-popup>
</template>

<script setup lang="ts">
import { invite } from '@/api/user';
import type { CourseInfo } from '@/api/user/types';
import { useUserStore } from '@/store';
import type { StageList } from '@/api/course/types';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },

  type: {
    type: Number,
    default: 1, // 1-开通会员 2-邀请好友
  },

  courseList: {
    type: Array as PropType<CourseInfo[] | StageList[]>,
    default: () => [],
  },

  stageCount: {
    type: Number,
    default: 99,
  },
});

const emit = defineEmits(['update:show', 'showVip', 'confirm']);

const userStore = useUserStore();
const choosedIds = ref<number[]>([]);
const handleChoose = (item: CourseInfo | StageList) => {
  if (choosedIds.value.includes(item.id)) {
    choosedIds.value = choosedIds.value.filter((id: number) => id !== item.id);
  }
  else {
    if (choosedIds.value.length >= props.stageCount) {
      uni.showToast({
        title: `您最多选择${props.stageCount}个阶段`,
        icon: 'none',
      });
      return;
    }
    choosedIds.value.push(item.id);
  }
};

const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};

const showVip = () => {
  emit('showVip');
  close();
};

// 确认 开通会员
const confirm = () => {
  emit('confirm', choosedIds.value);
  close();
};

// 分享
uni.showShareMenu({
  withShareTicket: true,
  menus: ['shareAppMessage', 'shareTimeline'],
});
onShareAppMessage(async () => {
  // TODO: 邀请达上限
  const code = await invite({ stageIds: choosedIds.value });
  const params = {
    userName: userStore.nickname,
    userAvatar: userStore.avatar,
    stageName: props.courseList.filter((item: CourseInfo | StageList) => choosedIds.value.includes(item.id)).map((item: CourseInfo | StageList) => item.name).join(' '),
    code: code.invitationCode,
  };
  return {
    title: '邀请你共享',
    path: `/pages/tab/home/<USER>
  };
});
</script>

<style lang="scss" scoped>
.choose-age {
  width: 100%;
  padding: 32rpx;
  background: #fff;

  display: flex;
  flex-direction: column;
  align-items: center;

  .age-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title-text {
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }

    .title-img {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .age-list {
    width: 100%;
    padding: 32rpx 0 20rpx 0;
    border-bottom: 2rpx solid #F5F6F9;;

    .list-item {
      height: 90rpx;
      padding: 0 16rpx 8rpx 16rpx;
      display: flex;
      align-items: center;

      .item-text {
        font-weight: bold;
        font-size: 28rpx;
        color: #303030;
      }

      .item-age {
        flex: 1;
        padding-left: 12rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #64706E;
      }

      .item-checkbox {
        width: 32rpx;
        height: 32rpx;
        border-radius: 4rpx;
        border: 2rpx solid #CCCCCC;
        background: #fff;

        display: flex;
        align-items: center;
        justify-content: center;
      }

      .checked {
        background-color: #7151ED;
        border-color: #7151ED;

        .up-icon {

        }
      }
    }
  }

  .age-btn {
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    width: 686rpx;
    height: 104rpx;
    background: #7151ED;
    border-radius: 16rpx;

    font-size: 32rpx;
    font-weight: bold;
    color: #FFFFFF;
    text-align: center;
    line-height: 104rpx;
  }

  .age-info {
    font-size: 32rpx;
    color: #7151ED;
    line-height: 48rpx;
  }
}
</style>

<style lang="scss">
choose-age {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }

  .u-icon__icon {
    font-size: 24rpx !important;
    color: #fff !important;
  }
}
</style>
