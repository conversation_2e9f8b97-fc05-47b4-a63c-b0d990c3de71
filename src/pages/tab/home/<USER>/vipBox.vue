<template>
  <up-popup :show="show" mode="center" :close-on-click-overlay="false" @close="close" @open="open">
    <view class="vip-box">
      <view class="vip-title">
        {{ title }}
      </view>
      <view class="vip-content">
        <view class="content-title">
          扫码二维码添加客服领取会员
        </view>
        <image class="content-img" src="" />
        <view class="content-text">
          <text class="text-info">
            此处是微信号
          </text>
          <view class="text-btn">
            复制微信号
          </view>
        </view>
        <view class="content-btn" @click="handleConvert">
          {{ btnText }}
        </view>
      </view>
    </view>
    <image class="vip-btn" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_close.png" @click="close" />
  </up-popup>
  <up-popup :show="vipShow" mode="center" :close-on-click-overlay="false" @close="closeVip" @open="openVip">
    <view class="member-box">
      <view class="member-title">
        兑换会员
      </view>
      <view class="member-content">
        <view class="content-title">
          填写会员码
        </view>
        <up-input
          v-model="vipNumber"
          placeholder="请输入会员兑换码"
          border="surround"
          placeholder-class="member-placeholder"
        />
      </view>
      <view class="member-footer">
        <view class="footer-btn btn-margin" @click="closeVip">
          取消
        </view>
        <view class="footer-btn" @click="confirmConvert">
          兑换
        </view>
      </view>
    </view>
  </up-popup>
  <chooseAge v-if="chooseAgeShow" v-model:show="chooseAgeShow" :course-list="stageList" :stage-count="stageCount" @show-vip="vipShow = true" @confirm="confirmVip" />
</template>

<script setup lang="ts">
import chooseAge from './chooseAge.vue';
import { exchangeCode, getCodeInfo } from '@/api/user';
import type { StageList } from '@/api/course/types';
import { useUserStore } from '@/store';

defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  convertShow: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '正课需要开通会员才能看哦！',
  },
  btnText: {
    type: String,
    default: '兑换会员',
  },
  stageList: {
    type: Array as PropType<StageList[]>,
    default: () => [],
  },
});

const emit = defineEmits(['update:show', 'updateData']);
const userStore = useUserStore();
const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};

const vipShow = ref<boolean>(props.convertShow);
const vipNumber = ref<string>('');
watch(() => props.convertShow, (newVal) => {
  vipShow.value = newVal;
});

const handleConvert = () => {
  vipShow.value = true;
};
const openVip = () => {
  vipShow.value = true;
};
const closeVip = () => {
  vipShow.value = false;
  vipNumber.value = '';
};
const confirmVip = (selectedStageIds?: number[]) => {
  exchangeCode({ exchangeCode: vipNumber.value, selectedStageIds }).then(() => {
    uni.showToast({
      title: '兑换成功',
    });
    userStore.info();
    emit('updateData');
    closeVip();
    close();
  });
};
const chooseAgeShow = ref(false);
const stageCount = ref(0);
// 兑换
const confirmConvert = () => {
  getCodeInfo({ exchangeCode: vipNumber.value }).then((res) => {
    if (res.needSelectStage) {
      chooseAgeShow.value = true;
      stageCount.value = res.stageCount;
    }
    else {
      confirmVip();
    }
  });
};
</script>

<style lang="scss" scoped>
.vip-box {
  width: 590rpx;
  height: 798rpx;
  padding: 48rpx 32rpx;
  background: linear-gradient( 135deg, #54CE89 0%, #C8F1C1 100%);
  border-radius: 40rpx;

  .vip-title {
    padding-bottom: 24rpx;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 48rpx;
    font-weight: bold;
    text-align: center;
  }

  .vip-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32rpx 24rpx;
    background: #fff;
    border-radius: 40rpx;

    .content-title {
      padding: 16rpx 0 20rpx 0;
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }

    .content-img {
      margin: 16rpx;
      width: 280rpx;
      height: 280rpx;
      border-radius: 20rpx;
      background: #D9D9D9;
    }

    .content-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20rpx;
      padding: 4rpx;
      border-radius: 8rpx;
      border: 1rpx solid #7151ED;

      .text-info {
        padding: 0 10rpx;
        font-size: 24rpx;
        color: #303030;
        line-height: 36rpx;
      }

      .text-btn {
        padding: 0 8rpx;
        background: #7151ED;
        border-radius: 8rpx;

        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 44rpx;
      }
    }

    .content-btn {
      padding-top: 56rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #7151ED;
      line-height: 42rpx;
    }
  }
}

.vip-btn {
  margin-top: 80rpx;
  width: 88rpx;
  height: 88rpx;
}

.member-box {
  width: 590rpx;
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;

  .member-title {
    font-weight: bold;
    font-size: 32rpx;
    color: #303030;
    line-height: 48rpx;
    text-align: center;
  }

  .member-content {
    .content-title {
      padding: 16rpx 0;
      font-size: 28rpx;
      color: #333333;
      line-height: 42rpx;
    }

    .member-placeholder {
      font-size: 28rpx;
      color: #64706E;
      line-height: 42rpx;
    }
  }

  .member-footer {
    padding-top: 60rpx;
    display: flex;
    justify-content: space-between;

    .footer-btn {
      padding: 0 83rpx;
      background: #7151ED;
      border: 2rpx solid #7151ED;
      border-radius: 8rpx;

      font-size: 32rpx;
      line-height: 80rpx;
      text-align: center;
      color: #FFFFFF;
    }

    .btn-margin {
      margin-right: 40rpx;
      background: #FFFFFF;
      color: #7151ED;
    }
  }
}
</style>

<style lang="scss">
vip-box {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }
}
</style>
