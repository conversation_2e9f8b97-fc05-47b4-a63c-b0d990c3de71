<template>
  <view class="user-list">
    <view v-for="item in memberList" :key="item.memberId" class="list-item">
      <view v-if="isRemove" class="item-radio" :class="[chooseList.includes(item.memberId) && 'item-choose']" @click="handleClick(item.memberId)" />
      <image class="item-img" :src="item.memberAvatar" />
      <text class="item-name">
        {{ item.memberNickname }}
      </text>
    </view>
    <view v-if="!isRemove" class="user-footer">
      <view class="btn-left" @click="handleRemove">
        <image class="btn-icon" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_remove.png" />
        <text class="btn-text">
          移除
        </text>
      </view>
      <view class="btn-right" open-type="share" @click="chooseAgeShow = true">
        邀请成员
      </view>
    </view>
    <view v-else class="user-footer">
      <view class="footer-btn btn-margin" @click="handleRemove">
        返回
      </view>
      <view class="footer-btn" @click="confirmRemove">
        移除
      </view>
    </view>
  </view>

  <chooseAge v-if="chooseAgeShow" v-model:show="chooseAgeShow" :type="2" :course-list="courseList" />
</template>

<script lang="ts" setup>
import chooseAge from '@/pages/tab/home/<USER>/chooseAge.vue';
import { getMembers, myCourses, removeMemberBatch } from '@/api/user';
import type { CourseInfo, MemberInfo } from '@/api/user/types';

const memberList = ref<MemberInfo[]>([]);
const courseList = ref<CourseInfo[]>([]);
const chooseAgeShow = ref(false);
const getList = () => {
  getMembers().then((res) => {
    memberList.value = res;
  });
  // 获取我的阶段列表
  myCourses().then(({ stages }: { stages: CourseInfo[] }) => {
    courseList.value = stages.filter((item: CourseInfo) => item.owned);
  });
};
onLoad(() => {
  getList();
});

const chooseList = ref<Array<number>>([]); // 已选择的成员id列表
const handleClick = (id: number) => {
  if (chooseList.value.includes(id)) {
    chooseList.value = chooseList.value.filter((i: number) => i !== id);
  }
  else {
    chooseList.value.push(id);
  }
};

const isRemove = ref<boolean>(false);
// 移除成员
const handleRemove = () => {
  isRemove.value = !isRemove.value;
  if (!isRemove.value) {
    chooseList.value = [];
  }
};

const confirmRemove = () => {
  uni.showModal({
    title: '提示',
    content: '确定要移除这些成员吗？',
    success: (res) => {
      if (res.confirm) {
        removeMemberBatch({ memberIds: chooseList.value }).then(() => {
          uni.showToast({
            title: '删除成功',
          });
          handleRemove();
          getList();
        });
      }
    },
  });
};

// 分享
uni.showShareMenu({
  withShareTicket: true,
  menus: ['shareAppMessage', 'shareTimeline'],
});
onShareAppMessage(() => {
  return {
    title: '邀请你共享',
    path: `/pages/tab/home/<USER>
  };
});
</script>

<style lang="scss" scoped>
.user-list {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0 40rpx;
  margin-bottom: 168rpx;
  overflow: auto;

  .list-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 2rpx dashed #DFE1E7;

    .item-radio {
      position: relative;
      margin-right: 24rpx;
      width: 48rpx;
      height: 48rpx;
      border-radius: 50%;
      border: 1rpx solid #7151ED;
      background: #fff;
    }

    .item-choose {
      &::after {
        position: absolute;
        top: 11rpx;
        left: 11rpx;
        content: '';
        width: 24rpx;
        height: 24rpx;
        border-radius: 50%;
        background: #7151ED;
      }
    }

    .item-img {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .item-name {
      padding-left: 24rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
  }

  .user-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 32rpx 40rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -20rpx 20rpx 0 rgba(0,0,0,0.08);

    .btn-left {
      width: 104rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-right: 40rpx;

      .btn-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .btn-text {
        padding-top: 8rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 42rpx;
      }
    }

    .btn-right {
      flex: 1;
      background: #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 104rpx;
      text-align: center;
      color: #FFFFFF;
    }

    .footer-btn {
      flex: 1;
      margin: 0 20rpx;
      background: #7151ED;
      border: 2rpx solid #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 100rpx;
      text-align: center;
      color: #FFFFFF;
    }

    .btn-margin {
      background: #FFFFFF;
      color: #7151ED;
    }
  }
}
</style>
