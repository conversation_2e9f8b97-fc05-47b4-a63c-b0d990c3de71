<template>
  <view class="application-detail">
    <view class="detail-title">
      <view class="title-left">
        <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_1.png" />
        <text class="title-text">
          跨学科应用
        </text>
      </view>

      <view class="title-right">
        <view class="title-btn" :class="{ 'active-btn': activeBtn === 0 }" @click="activeBtn = 0">
          原题
        </view>
        <view class="title-btn" :class="{ 'active-btn': activeBtn === 1 }" @click="activeBtn = 1">
          解析
        </view>
      </view>
    </view>

    <view class="detail-content">
      <view v-for="(item, index) in contentUseDetail" :key="index" class="detail-item">
        <view class="item-title">
          {{ item.topic }}
        </view>

        <view v-for="(question, quesIndex) in item.questions" :key="`question${index}${quesIndex}`" class="item-question">
          <view class="question-type">
            <view class="type-label" :class="[`type-label-${quesIndex + 1}`]">
              <text class="label-text">
                {{ getType(question.question_type) }}
              </text>
            </view>
            <text class="type-text">
              {{ question.question_title_type }}
            </text>
          </view>
          <view class="question-info">
            <view class="question-title">
              {{ question.question_title }}
            </view>
            <!-- 选择 -->
            <template v-if="question.question_type === 1">
              <view v-for="text in question.options" :key="text" class="question-option">
                <text class="option-text">
                  {{ text }}
                </text>
              </view>
            </template>

            <!-- 填空 -->
            <view v-if="question.question_type === 2" class="question-desc">
              （在空格处填入答案）
            </view>

            <!-- 连线 -->
            <template v-if="question.question_type === 3">
              <view v-for="(text, textIndex) in question.lef_options" :key="textIndex" class="question-option">
                <text class="option-text">
                  {{ text }}
                </text>
                <text class="option-text text-right">
                  {{ question.right_options[textIndex] }}
                </text>
              </view>
            </template>

            <!-- 画一画 -->
            <!-- <view class="question-img">
              <view class="img-item">
                <image class="image" src="" />
                <text class="text">
                  A.高水位清水
                </text>
              </view>
              <text class="img-desc">
                VS
              </text>
              <view class="img-item">
                <image class="image" src="" />
                <text class="text">
                  B.低水位盐水
                </text>
              </view>
            </view> -->
          </view>
          <view v-if="activeBtn === 1" class="question-answer">
            <view class="answer-title">
              解析
            </view>
            <view class="answer-content">
              {{ question.answer }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="detail-footer">
      <view class="btn-left" @click="handleCopy">
        <image class="btn-icon" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_copy.png" />
        <text class="btn-text">
          拷贝
        </text>
      </view>
      <view class="btn-right" @click="handleDownload">
        下载文档
      </view>
    </view>

    <downloadGuide v-if="showGuide" @open-preview="openPreview" @close="showGuide = false" />
  </view>
</template>

<script lang="ts" setup>
import downloadGuide from '@/components/download-guide/index.vue';
import { useCourseStore } from '@/store';
import type { InterdisciplinaryModel } from '@/api/course/types';
import { useClipboard } from '@/hooks';

const { setClipboardData } = useClipboard();
const courseStore = useCourseStore();
const contentUseDetail = ref<InterdisciplinaryModel[] | null>();
contentUseDetail.value = courseStore.contentUseDetail;

const activeBtn = ref<number>(0);

const getType = (type: number) => {
  switch (type) {
    case 1:
      return '连线题';
    case 2:
      return '选择题';
    case 3:
      return '图画算式';
    case 4:
      return '圈一圈';
    default:
      return '';
  }
};

const handleCopy = async () => {
  try {
    await setClipboardData({
      data: courseStore.contentUseDetailCopy,
      showToast: true,
    });
  }
  catch (error) {
    console.error('复制失败:', error);
  }
};

const showGuide = ref(false);
const openPreview = () => {
  showGuide.value = false;
  uni.showLoading({
    title: '正在下载...',
  });
  uni.downloadFile({
    url: courseStore.contentUseUrl,
    success: (res: any) => {
      const filePath = res.tempFilePath;
      // 打开pdf
      uni.openDocument({
        filePath: encodeURI(filePath),
        showMenu: true,
        success() {
          uni.hideLoading();
          console.log('打开文档成功');
        },
        fail: () => {
          uni.hideLoading();
          uni.showToast({
            title: '打开失败',
            duration: 1500,
            icon: 'none',
          });
          console.log('打开失败');
        },
      });
    },
    fail: () => {
      uni.hideLoading();
      uni.showToast({
        title: '下载失败',
        duration: 1500,
        icon: 'none',
      });
      console.log('下载失败');
    },
  });
};
const handleDownload = () => {
  // if (!loginFlag.value) {
  //   uni.showToast({
  //     title: '请先登录',
  //     duration: 1500,
  //     icon: 'none',
  //   });
  //   setTimeout(() => {
  //     uni.reLaunch({ url: '/pages/login/index' });
  //   }, 1500);
  //   return;
  // }

  showGuide.value = true;
};
</script>

<style lang="scss" scoped>
.application-detail {
  position: relative;
  height: 100%;
  padding: 40rpx 40rpx 0 40rpx;
  display: flex;
  flex-direction: column;

  .detail-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 40rpx;

    .title-left {
      display: flex;
      align-items: center;

      .title-img {
        width: 56rpx;
        height: 56rpx;
      }

      .title-text {
        padding-left: 4rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #303030;
      }
    }

    .title-right {
      width: 320rpx;
      height: 66rpx;
      padding: 2rpx 4rpx;
      display: flex;
      border-radius: 100rpx;
      border: 2rpx solid #7151ED;

      .title-btn {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        line-height: 58rpx;
        color: #000000;
        border-radius: 100rpx;
      }

      .active-btn {
        background: #7151ED;
        color: #fff;
      }
    }
  }

  .detail-content {
    flex: 1;
    padding-bottom: 168rpx;
    overflow: auto;

    .detail-item {
      padding: 48rpx;
      background: #FFFFFF;
      box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
      border-radius: 40rpx;
      margin-bottom: 64rpx;

      .item-title {
        padding-bottom: 24rpx;
        font-size: 32rpx;
        color: #303030;
        line-height: 48rpx;
        font-weight: bold;
        border-bottom: 2rpx dashed #E6D6FF;
      }

      .item-question {
        padding-top: 40rpx;

        .question-type {
          display: flex;
          align-items: center;

          .type-label {
            display: flex;
            height: 36rpx;
            padding: 0 16rpx;
            background: #13A86C;
            border-radius: 4rpx;
            transform: skewX(-15deg);

            .label-text {
              font-weight: 500;
              font-size: 24rpx;
              color: #FFFFFF;
              line-height: 36rpx;
              transform: skewX(15deg);
            }
          }

          .type-label-2 {
            background: #397EF5;
          }

          .type-label-3 {
            background: #F69C0B;
          }

          .type-label-4 {
            background: #A83EBF;
          }

          .type-text {
            padding-left: 16rpx;
            font-size: 32rpx;
            color: #333333;
            line-height: 48rpx;
          }
        }

        .question-info {
          margin-top: 44rpx;

          .question-title {
            font-size: 28rpx;
            color: #333333;
            line-height: 42rpx;
            padding-bottom: 4rpx;
          }

          .question-option {
            padding-top: 24rpx;
            display: flex;
            justify-content: space-between;

            .option-text {
              font-size: 28rpx;
              color: #333333;
              line-height: 42rpx;
            }

            .text-right {
              text-align: left;
              padding-left: 40rpx;
            }
          }

          .question-desc {
            padding-top: 24rpx;
            font-size: 28rpx;
            color: rgba(51,51,51,0.7);
            line-height: 42rpx;
          }

          .question-img {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 24rpx;

            .img-item {
              .image {
                width: 237rpx;
                height: 178rpx;
                background: #D9D9D9;
              }

              .text {
                display: block;
                padding-top: 8rpx;
                font-size: 28rpx;
                color: #333333;
                line-height: 42rpx;
              }
            }

            .img-desc {
              font-size: 28rpx;
              color: #333333;
              line-height: 42rpx;
            }
          }
        }

        .question-answer {
          margin-top: 28rpx;
          padding: 24rpx;
          background: #F6F8FA;
          border-radius: 16rpx;

          .answer-title {
            position: relative;
            padding-left: 24rpx;
            font-size: 28rpx;
            color: #333333;
            line-height: 42rpx;

            &::before {
              content: '';
              position: absolute;
              top: 9rpx;
              left: 0;
              width: 8rpx;
              height: 24rpx;
              background: #7151ED;
            }
          }

          .answer-content {
            padding-top: 28rpx;
            font-size: 28rpx;
            color: #333333;
            line-height: 42rpx;
          }
        }
      }
    }
  }

  .detail-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 32rpx 40rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -20rpx 20rpx 0 rgba(0,0,0,0.08);

    .btn-left {
      width: 104rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-right: 40rpx;

      .btn-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .btn-text {
        padding-top: 8rpx;
        font-size: 28rpx;
        color: #333333;
        line-height: 42rpx;
      }
    }

    .btn-right {
      flex: 1;
      background: #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 104rpx;
      text-align: center;
      color: #FFFFFF;
    }
  }
}
</style>
