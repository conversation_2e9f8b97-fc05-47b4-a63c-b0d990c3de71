<template>
  <view class="task-detail">
    <view class="detail-title">
      <view class="title-left">
        <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_${currentIndex + 5}.png`" />
        <text class="title-text">
          {{ currentIndex === 1 ? '课后发散' : '课后反馈' }}
        </text>
      </view>

      <!-- <view v-if="currentIndex === 1" class="title-right">
        <view class="title-btn" :class="{ 'active-btn': activeBtn === 0 }" @click="activeBtn = 0">
          任务
        </view>
        <view class="title-btn" :class="{ 'active-btn': activeBtn === 1 }" @click="activeBtn = 1">
          解析
        </view>
      </view> -->
    </view>

    <view class="detail-content">
      <view v-for="(item, index) in contentDiffuseDetail" :key="index" class="detail-item">
        <view class="item-title">
          <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${index + 1}.png`" />
          <text class="info-text">
            {{ item.name }}
          </text>
        </view>

        <view class="item-content">
          <view v-for="(text, textIndex) in item.intro_detail" :key="`${textIndex}text${index}`" class="question-option">
            <text class="option-text">
              {{ text.split(':')[0] }}：
            </text>
            <text class="option-text text-right">
              {{ text.split(':')[1] }}
            </text>
          </view>
          <!-- <view class="content-btn">
            <view class="btn-item" @click="handleCopy">
              拷贝
            </view>
          </view> -->
        </view>
      </view>

      <view class="detail-footer">
        <view class="footer-btn" @click="handleCopy">
          全文拷贝
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useClipboard } from '@/hooks';
import { useCourseStore } from '@/store';
import type { AfterclassQuestion } from '@/api/course/types';

const courseStore = useCourseStore();
const contentDiffuseDetail = ref<AfterclassQuestion[] | null>();

const { setClipboardData } = useClipboard();

// const activeBtn = ref<number>(0);

const handleCopy = async () => {
  try {
    await setClipboardData({
      data: courseStore.contentDiffuseDetailCopy,
      showToast: true,
    });
  }
  catch (error) {
    console.error('复制失败:', error);
  }
};

const currentIndex = ref<number>(1);
onLoad((options: any) => {
  currentIndex.value = Number(options.index) || 1;
  contentDiffuseDetail.value = courseStore.contentDiffuseDetail?.filter((item: AfterclassQuestion) => item.type === currentIndex.value);
  if (currentIndex.value === 1) {
    uni.setNavigationBarTitle({
      title: '课后发散',
    });
  }
  else {
    uni.setNavigationBarTitle({
      title: '课后反馈',
    });
  }
});
</script>

<style lang="scss" scoped>
.task-detail {
  position: relative;
  height: 100%;
  padding: 40rpx 40rpx 0 40rpx;
  display: flex;
  flex-direction: column;

  .detail-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 40rpx;

    .title-left {
      display: flex;
      align-items: center;

      .title-img {
        width: 56rpx;
        height: 56rpx;
      }

      .title-text {
        padding-left: 4rpx;
        font-weight: bold;
        font-size: 32rpx;
        color: #303030;
      }
    }

    .title-right {
      width: 320rpx;
      height: 66rpx;
      padding: 2rpx 4rpx;
      display: flex;
      border-radius: 100rpx;
      border: 2rpx solid #7151ED;

      .title-btn {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        line-height: 58rpx;
        color: #000000;
        border-radius: 100rpx;
      }

      .active-btn {
        background: #7151ED;
        color: #fff;
      }
    }
  }

  .detail-content {
    flex: 1;
    padding-bottom: 168rpx;
    overflow: auto;

    .detail-item {
      padding: 48rpx 48rpx 64rpx 48rpx;
      background: #FFFFFF;
      box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
      border-radius: 40rpx;
      margin-bottom: 64rpx;

      .item-title {
        display: flex;
        padding-bottom: 24rpx;
        border-bottom: 2rpx dashed #E6D6FF;

        .info-img {
          width: 24rpx;
          height: 42rpx;
        }

        .info-text {
          flex: 1;
          padding-left: 16rpx;
          font-size: 28rpx;
          color: #303030;
          line-height: 42rpx;
        }
      }

      .item-content {
        margin-top: 40rpx;

        .question-option {
          padding-top: 28rpx;
          display: flex;

          .option-text {
            font-size: 28rpx;
            color: #303030;
            line-height: 42rpx;
            padding-right: 16rpx;
          }

          .text-right {
            flex: 1;
            color: rgba(51, 51, 51, 0.7);
            text-align: left;
            white-space: normal; // 允许文字换行
            word-break: break-all; // 文字换行规则
          }
        }

        .question-option:first-child {
          padding-top: 0;
        }

        .content-btn {
          display: flex;
          justify-content: flex-end;
          padding-top: 40rpx;

          .btn-item {
            padding: 0 32rpx;
            border-radius: 16rpx;
            border: 2rpx solid #7151ED;

            font-weight: 500;
            font-size: 28rpx;
            line-height: 62rpx;
            color: #7151ED;
          }
        }
      }
    }
  }

  .detail-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 32rpx 40rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -20rpx 20rpx 0 rgba(0,0,0,0.08);

    .footer-btn {
      flex: 1;
      border: 2rpx solid #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 100rpx;
      text-align: center;
      color: #7151ED;
    }
  }
}
</style>
