<template>
  <view class="plan-detail">
    <view class="detail-progress">
      <view v-for="item in 5" :key="item" class="progress-item" :style="{ background: item === currentIndex ? '#7151ED' : '#D8D8D8' }" />
    </view>

    <view class="detail-title">
      <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_${currentIndex}.png`" />
      <text class="title-text">
        {{ currentIndex === 1 ? '课程预告' : currentIndex === 2 ? '材料清单' : currentIndex === 3 ? '悬念导入' : currentIndex === 4 ? '实施' : '原理总结' }}
      </text>
    </view>

    <view class="detail-content-scroller">
      <view v-if="currentIndex > 2" class="detail-info">
        <text class="info-text">
          目标：
        </text>
        <text class="info-text">
          {{ currentIndex === 3 ? contentPlanDetail?.suspense_introduction_aim : currentIndex === 4 ? contentPlanDetail?.experiment_implementation_aim : contentPlanDetail?.principle_summary_aim }}
        </text>
      </view>
      <view v-if="currentIndex === 1" class="detail-content">
        <view v-for="(step, index) in contentPlanDetail?.course_preview" :key="`preview-${index}`" class="content-step">
          <view class="info-title">
            <!-- <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" /> -->
            <text class="info-text no-padding">
              {{ step.title }}
            </text>
          </view>
          <view class="item-subtitle no-padding">
            {{ step.description }}
          </view>
        </view>
      </view>
      <view v-else-if="currentIndex === 2" class="detail-content">
        <view v-for="(step, index) in contentPlanDetail?.materials_list" :key="`materials-${index}`" class="content-step">
          <view class="info-title">
            <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" />
            <text class="info-text">
              {{ step.title }}
            </text>
          </view>
          <view class="item-subtitle">
            {{ step.description }}
          </view>
        </view>

        <view class="content-tips">
          <view class="tips-text">
            Tipes：
          </view>
          <view class="tips-text">
            {{ contentPlanDetail?.materials_tip }}
          </view>
        </view>
      </view>
      <template v-else-if="currentIndex === 3">
        <view v-for="(step, index) in contentPlanDetail?.suspense_introduction_steps" :key="`suspense-${index}`" class="detail-content">
          <view class="item-index">
            {{ (index + 1).toString().padStart(2, '0') }}/
          </view>
          <view class="item-title">
            <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" />
            <text class="title-text">
              {{ step.title }}
            </text>
          </view>
          <view class="item-desc">
            {{ step.description }}
          </view>
        </view>
      </template>
      <template v-else-if="currentIndex === 4">
        <view v-for="(step, index) in contentPlanDetail?.experiment_implementation_steps" :key="`experiment-${index}`" class="detail-content">
          <view class="item-index">
            {{ (index + 1).toString().padStart(2, '0') }}/
          </view>
          <view class="item-title">
            <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" />
            <text class="title-text">
              {{ step.title }}
            </text>
          </view>
          <view class="item-desc">
            {{ step.description }}
          </view>
        </view>
      </template>
      <template v-else-if="currentIndex === 5">
        <view v-for="(step, index) in contentPlanDetail?.principle_summary_steps" :key="`principle-${index}`" class="detail-content">
          <view class="item-index">
            {{ (index + 1).toString().padStart(2, '0') }}/
          </view>
          <view class="item-title">
            <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" />
            <text class="title-text">
              {{ step.title }}
            </text>
          </view>
          <view class="item-desc">
            {{ step.description }}
          </view>
        </view>
      </template>
    </view>

    <view class="detail-footer">
      <view v-if="currentIndex > 1" class="footer-btn btn-left" @click="currentIndex--">
        上一步
      </view>
      <view v-if="currentIndex < 5" class="footer-btn" @click="currentIndex++">
        下一步
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useCourseStore } from '@/store';
import type { CoursePlan } from '@/api/course/types';

const courseStore = useCourseStore();

const currentIndex = ref<number>(1);
const contentPlanDetail = ref<CoursePlan | null>();

// 接收路由参数
onLoad((options: any) => {
  currentIndex.value = Number(options.index) || 1;
  contentPlanDetail.value = courseStore.contentPlanDetail;
});
</script>

<style lang="scss" scoped>
.plan-detail {
  position: relative;
  height: 100%;
  padding: 40rpx 40rpx 0 40rpx;
  display: flex;
  flex-direction: column;

  .detail-progress {
    display: flex;

    .progress-item {
      flex: 1;
      margin: 0 2rpx;
      height: 16rpx;
      border-radius: 8rpx;
      background: #D8D8D8;
    }
  }

  .detail-title {
    display: flex;
    align-items: center;
    padding: 80rpx 0 40rpx 0;

    .title-img {
      width: 56rpx;
      height: 56rpx;
    }

    .title-text {
      padding-left: 8rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
    }
  }

  .detail-content-scroller {
    flex: 1;
    padding-bottom: 168rpx;
    overflow: auto;

    .detail-info {
      display: flex;
      flex-direction: column;
      padding-bottom: 56rpx;

      .info-text {
        font-size: 28rpx;
        color: #64706E;
        line-height: 42rpx;
      }
    }

    .detail-content {
      padding: 48rpx;
      background: #FFFFFF;
      box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
      border-radius: 40rpx;
      margin-bottom: 64rpx;

      .content-step {
        padding-bottom: 40rpx;

        .info-title {
          display: flex;
          padding-bottom: 16rpx;

          .info-img {
            width: 24rpx;
            height: 42rpx;
          }

          .info-text {
            flex: 1;
            padding-left: 16rpx;
            font-size: 28rpx;
            color: #303030;
            line-height: 42rpx;
          }
        }

        .item-subtitle {
          padding-left: 40rpx;
          font-size: 28rpx;
          color: #64706E;
          line-height: 42rpx;
        }
      }

      .content-tips {
        padding-top: 24rpx;
        border-top: 2rpx dashed #E6D6FF;

        .tips-text {
          font-size: 28rpx;
          color: #64706E;
          line-height: 42rpx;
        }
      }

      .item-index {
        padding-bottom: 16rpx;
        font-size: 28rpx;
        color: #64706E;
        line-height: 42rpx;
      }

      .item-title {
        display: flex;
        align-items: center;
        padding-bottom: 40rpx;
        border-bottom: 2rpx dashed #E6D6FF;

        .title-img {
          width: 24rpx;
          height: 42rpx;
        }

        .title-text {
          flex: 1;
          padding-left: 8rpx;
          font-weight: bold;
          font-size: 32rpx;
          color: #303030;
        }
      }

      .item-desc {
        padding-top: 24rpx;
        font-size: 28rpx;
        color: #64706E;
        line-height: 42rpx;
      }
    }

    .no-padding {
      padding: 0 !important;
    }
  }

  .detail-footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 32rpx 20rpx;
    display: flex;
    background: #fff;
    box-shadow: 0 -20rpx 20rpx 0 rgba(0,0,0,0.08);

    .footer-btn {
      flex: 1;
      margin: 0 20rpx;
      background: #7151ED;
      border: 2rpx solid #7151ED;
      border-radius: 16rpx;

      font-size: 32rpx;
      line-height: 100rpx;
      text-align: center;
      color: #FFFFFF;
    }

    .btn-left {
      background: #FFFFFF;
      color: #7151ED;
    }
  }
}
</style>
