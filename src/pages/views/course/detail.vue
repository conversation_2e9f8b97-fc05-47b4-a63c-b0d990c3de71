<template>
  <view class="course-detail">
    <view class="course-top">
      <image class="top-bg" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_img_${currentIndex}.png`" />
      <view class="top-subTitle">
        小学科学实验教案
      </view>
      <view class="top-title">
        {{ courseStore?.name }}
      </view>
    </view>
    <TabBar v-model="currentTab" :list="tabList" active-color="7151ED" icon-url="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_3.png" />
    <view class="course-content">
      <lessonPlan v-if="currentTab === 0" :data="courseStore?.contentPlanSummary" :course-preview-copy="courseStore?.coursePreviewCopy" />
      <application v-if="currentTab === 1" :data="courseStore?.contentUseSummary" :content-use-detail-copy="courseStore?.contentUseDetailCopy" />
      <task v-if="currentTab === 2" :data="courseStore?.contentDiffuseSummary" :content-diffuse-detail-copy="courseStore?.contentDiffuseDetailCopy" />
    </view>
  </view>
</template>

<script setup lang="ts">
import lessonPlan from './components/lessonPlan.vue';
import application from './components/application.vue';
import task from './components/task.vue';
import TabBar from '@/components/tab-bar/index.vue';
import { useCourseStore } from '@/store';

const courseStore = useCourseStore();

const currentTab = ref(0);
const tabList = ref([
  { name: '课程&教案', id: 1 },
  { name: '跨学科应用', id: 2 },
  { name: '课后任务', id: 3 },
]);

const currentIndex = ref<number>(1);

const getData = async (id: string | number) => {
  await courseStore.getCourseDetail(id);
  console.log(courseStore.contentUseSummary);
};

onLoad((options: any) => {
  getData(options.id);
});
</script>

<style lang="scss" scoped>
.course-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 12rpx 32rpx 0 32rpx;

  .course-top {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 310rpx;
    padding: 62rpx 32rpx;
    margin-bottom: 64rpx;

    .top-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 686rpx;
      height: 310rpx;
    }

    .top-subTitle {
      position: relative;
      width: 316rpx;
      font-size: 28rpx;
      color: #FFFFFF;
      line-height: 42rpx;
    }

    .top-title {
      position: relative;
      width: 316rpx;
      font-size: 48rpx;
      color: #FFFFFF;
      line-height: 72rpx;
    }
  }

  .course-content {
    flex: 1;
    margin-top: 50rpx;
    overflow-y: auto;
  }
}
</style>
