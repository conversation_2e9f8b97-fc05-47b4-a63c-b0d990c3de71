<template>
  <view v-for="(item, index) in data" :key="index" class="content-item">
    <view class="item-title" @click="toDetail(item.type)">
      <image class="title-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_${item.type + 5}.png`" />
      <text class="title-text">
        {{ item.type === 1 ? '课后发散' : '课后反馈' }}
      </text>
    </view>
    <view class="item-content" @click="toDetail(item.type)">
      <view class="module-title">
        {{ item.name }}
      </view>
      <view class="question-info">
        <view v-for="(text, textIndex) in item.intro_detail" :key="`text${textIndex}`" class="question-option">
          <text class="option-text">
            {{ text.split(':')[0] }}：
          </text>
          <text class="option-text text-right">
            {{ text.split(':')[1] }}
          </text>
        </view>
      </view>
    </view>

    <view class="item-btn">
      <view class="btn-item" @click="handleCopy">
        拷贝
      </view>
      <view class="btn-item btn-right" @click="toDetail(item.type)">
        查看全部
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => {},
  },

  contentDiffuseDetailCopy: {
    type: String,
    default: '',
  },
});

const { setClipboardData } = useClipboard();
const handleCopy = async () => {
  try {
    await setClipboardData({
      data: props.contentDiffuseDetailCopy,
      showToast: true,
    });
  }
  catch (error) {
    console.error('复制失败:', error);
  }
};

const toDetail = (index: number) => {
  uni.navigateTo({
    url: `/pages/views/task/detail?index=${index}`,
  });
};
</script>

<style lang="scss" scoped>
.content-item {
  padding: 48rpx 48rpx 64rpx 48rpx;
  background: #FFFFFF;
  box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
  border-radius: 40rpx;
  margin-bottom: 64rpx;

  .item-title {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;
    border-bottom: 2rpx dashed #E6D6FF;

    .title-img {
      width: 56rpx;
      height: 56rpx;
    }

    .title-text {
      padding-left: 8rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
    }
  }

  .item-content {
    padding: 40rpx 0;

    .module-title {
      font-size: 32rpx;
      color: #333;
      line-height: 48rpx;
    }

    .question-info {
      margin-top: 28rpx;
      padding: 16rpx;
      background: #F6F8FA;
      border-radius: 16rpx;

      .question-option {
        padding-top: 28rpx;
        display: flex;

        .option-text {
          font-size: 28rpx;
          color: #303030;
          line-height: 42rpx;
          padding-right: 16rpx;
        }

        .text-right {
          flex: 1;
          color: rgba(51, 51, 51, 0.7);
          text-align: left;
          white-space: normal; // 允许文字换行
          word-break: break-all; // 文字换行规则
        }
      }

      .question-option:first-child {
        padding-top: 0;
      }
    }
  }

  .item-btn {
    display: flex;
    justify-content: flex-end;

    .btn-item {
      padding: 0 32rpx;
      border-radius: 16rpx;
      border: 2rpx solid #7151ED;

      font-weight: 500;
      font-size: 28rpx;
      line-height: 62rpx;
      color: #7151ED;
    }

    .btn-right {
      margin-left: 40rpx;
    }
  }
}
</style>
