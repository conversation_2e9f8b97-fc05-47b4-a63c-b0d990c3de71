<template>
  <view class="content-item" @click="toDetail(1)">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_1.png" />
      <text class="title-text">
        课程预告
      </text>
    </view>
    <view class="item-desc">
      {{ data?.course_preview }}
    </view>
    <view class="item-bottom">
      <view class="item-btn" @click.stop="handleCopy">
        拷贝预告
      </view>
    </view>
  </view>

  <view class="content-item" @click="toDetail(2)">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_2.png" />
      <text class="title-text">
        材料清单
      </text>
    </view>
    <view class="item-content">
      <view v-for="(item, index) in data?.materials" :key="`material-${index}`" class="item-info">
        <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${(index + 1) % 5 || 5}.png`" />
        <text class="info-text">
          {{ item }}
        </text>
      </view>
    </view>
  </view>

  <view class="content-item" @click="toDetail(3)">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_3.png" />
      <text class="title-text">
        悬念导入
      </text>
    </view>
    <view class="item-content">
      <view v-for="(item, index) in data?.suspense_introduction_steps" :key="`suspense-${index}`" class="item-info">
        <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${index + 1}.png`" />
        <text class="info-text">
          {{ item }}
        </text>
      </view>
    </view>
  </view>

  <view class="content-item" @click="toDetail(4)">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_4.png" />
      <text class="title-text">
        实施
      </text>
    </view>
    <view class="item-content">
      <view v-for="(item, index) in data?.experiment_implementation_steps" :key="`experiment-${index}`" class="item-info">
        <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${index + 1}.png`" />
        <text class="info-text">
          {{ item }}
        </text>
      </view>
    </view>
  </view>

  <view class="content-item" @click="toDetail(5)">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_5.png" />
      <text class="title-text">
        原理总结
      </text>
    </view>
    <view class="item-content">
      <view v-for="(item, index) in data?.principle_summary_steps" :key="`principle-${index}`" class="item-info">
        <image class="info-img" :src="`https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_step_${index + 1}.png`" />
        <text class="info-text">
          {{ item }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
// import type { CoursePLanSummary } from '@/api/course/types';

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => {},
  },

  coursePreviewCopy: {
    type: String,
    default: '',
  },
});

const { setClipboardData } = useClipboard();
const handleCopy = async () => {
  try {
    await setClipboardData({
      data: props.coursePreviewCopy,
      showToast: true,
    });
  }
  catch (error) {
    console.error('复制失败:', error);
  }
};

const toDetail = (item: number) => {
  uni.navigateTo({
    url: `/pages/views/lessonPlan/detail?index=${item}`,
  });
};
</script>

<style lang="scss" scoped>
.content-item {
  padding: 48rpx 48rpx 64rpx 48rpx;
  background: #FFFFFF;
  box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
  border-radius: 40rpx;
  margin-bottom: 64rpx;

  .item-title {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;
    border-bottom: 2rpx dashed #E6D6FF;

    .title-img {
      width: 56rpx;
      height: 56rpx;
    }

    .title-text {
      padding-left: 8rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
    }
  }

  .item-desc {
    padding: 40rpx 0;
    font-size: 28rpx;
    color: #303030;
    line-height: 42rpx;
  }

  .item-bottom {
    display: flex;
    justify-content: flex-end;

    .item-btn {
      padding: 0 32rpx;
      border-radius: 16rpx;
      border: 2rpx solid #7151ED;

      font-weight: 500;
      font-size: 28rpx;
      line-height: 62rpx;
      color: #7151ED;
    }
  }

  .item-content {
    .item-info {
      padding-top: 40rpx;
      display: flex;

      .info-img {
        width: 24rpx;
        height: 42rpx;
      }

      .info-text {
        flex: 1;
        padding-left: 16rpx;
        font-size: 28rpx;
        color: #303030;
        line-height: 42rpx;
      }
    }
  }
}
</style>
