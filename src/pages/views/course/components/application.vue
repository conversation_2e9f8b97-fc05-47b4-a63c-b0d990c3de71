<template>
  <view class="content-item">
    <view class="item-title">
      <image class="title-img" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/course_icon_1.png" />
      <text class="title-text">
        跨学科应用
      </text>
    </view>
    <view class="item-content">
      <view v-for="(content, contentIndex) in data" :key="`${contentIndex}content`" class="item-list">
        <view class="module-title">
          一、{{ content.topic }}
        </view>
        <view v-for="(item, index) in content.questions" :key="index" class="item-question" @click="toDetail">
          <view class="question-type">
            <view class="type-label" :class="[`type-label-${index + 1}`]">
              <text class="label-text">
                {{ getType(item.question_type) }}
              </text>
            </view>
            <text class="type-text">
              {{ item.question_title_type }}
            </text>
          </view>
          <view class="question-info">
            <view class="question-title">
              {{ item.question_title }}
            </view>
            <!-- 选择 -->
            <template v-if="item.question_type === 1">
              <view v-for="text in item.options" :key="text" class="question-option">
                <text class="option-text">
                  {{ text }}
                </text>
              </view>
            </template>

            <!-- 填空 -->
            <view v-if="item.question_type === 2" class="question-desc">
              （在空格处填入答案）
            </view>

            <!-- 连线 -->
            <template v-if="item.question_type === 3">
              <view v-for="(text, textIndex) in item.lef_options" :key="textIndex" class="question-option">
                <text class="option-text">
                  {{ text }}
                </text>
                <text class="option-text text-right">
                  {{ item.right_options[textIndex] }}
                </text>
              </view>
            </template>

            <!-- 画一画 -->
            <!-- <view class="question-img">
              <view class="img-item">
                <image class="image" src="" />
                <text class="text">
                  A.高水位清水
                </text>
              </view>
              <text class="img-desc">
                VS
              </text>
              <view class="img-item">
                <image class="image" src="" />
                <text class="text">
                  B.低水位盐水
                </text>
              </view>
            </view> -->
          </view>
        </view>
      </view>
    </view>

    <view class="item-btn">
      <view class="btn-item" @click="handleCopy">
        拷贝
      </view>
      <view class="btn-item btn-right" @click="toDetail">
        查看更多
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useClipboard } from '@/hooks';
// import type { InterdisciplinaryModel } from '@/api/course/types';

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => {},
  },

  contentUseDetailCopy: {
    type: String,
    default: '',
  },
});

const { setClipboardData } = useClipboard();

const handleCopy = async () => {
  try {
    await setClipboardData({
      data: props.contentUseDetailCopy,
      showToast: true,
    });
  }
  catch (error) {
    console.error('复制失败:', error);
  }
};

const getType = (type: number) => {
  switch (type) {
    case 1:
      return '选择题';
    case 2:
      return '问答题';
    case 3:
      return '连线题';
    default:
      return '';
  }
};

const toDetail = () => {
  uni.navigateTo({
    url: '/pages/views/application/detail',
  });
};
</script>

<style lang="scss" scoped>
.content-item {
  padding: 48rpx 48rpx 64rpx 48rpx;
  background: #FFFFFF;
  box-shadow: 0 8rpx 8rpx 0 rgba(83,92,246,0.08);
  border-radius: 40rpx;
  margin-bottom: 64rpx;

  .item-title {
    display: flex;
    align-items: center;
    padding-bottom: 24rpx;
    border-bottom: 2rpx dashed #E6D6FF;

    .title-img {
      width: 56rpx;
      height: 56rpx;
    }

    .title-text {
      padding-left: 8rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
    }
  }

  .item-content {
    .item-list {
      padding: 40rpx 0 24rpx 0;

      .module-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #303030;
        line-height: 48rpx;
      }

      .item-question {
        padding-top: 40rpx;

        .question-type {
          display: flex;
          align-items: center;

          .type-label {
            display: flex;
            height: 36rpx;
            padding: 0 16rpx;
            background: #13A86C;
            border-radius: 4rpx;
            transform: skewX(-15deg);

            .label-text {
              font-weight: 500;
              font-size: 24rpx;
              color: #FFFFFF;
              line-height: 36rpx;
              transform: skewX(15deg);
            }
          }

          .type-label-2 {
            background: #397EF5;
          }

          .type-label-3 {
            background: #F69C0B;
          }

          .type-label-4 {
            background: #A83EBF;
          }

          .type-text {
            padding-left: 16rpx;
            font-size: 32rpx;
            color: #333333;
            line-height: 48rpx;
          }
        }

        .question-info {
          margin-top: 28rpx;
          padding: 16rpx;
          background: #F6F8FA;
          border-radius: 16rpx;

          .question-title {
            font-size: 28rpx;
            color: #333333;
            line-height: 42rpx;
            padding-bottom: 4rpx;
          }

          .question-option {
            padding-top: 24rpx;
            display: flex;
            justify-content: space-between;

            .option-text {
              font-size: 28rpx;
              color: #333333;
              line-height: 42rpx;
            }

            .text-right {
              text-align: left;
              padding-left: 40rpx;
            }
          }

          .question-desc {
            padding-top: 24rpx;
            font-size: 28rpx;
            color: rgba(51,51,51,0.7);
            line-height: 42rpx;
          }

          .question-img {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 24rpx;

            .img-item {
              .image {
                width: 237rpx;
                height: 178rpx;
                background: #D9D9D9;
              }

              .text {
                display: block;
                padding-top: 8rpx;
                font-size: 28rpx;
                color: #333333;
                line-height: 42rpx;
              }
            }

            .img-desc {
              font-size: 28rpx;
              color: #333333;
              line-height: 42rpx;
            }
          }
        }
      }
    }
  }

  .item-btn {
    display: flex;
    justify-content: flex-end;
    padding-top: 16rpx;

    .btn-item {
      padding: 0 32rpx;
      border-radius: 16rpx;
      border: 2rpx solid #7151ED;

      font-weight: 500;
      font-size: 28rpx;
      line-height: 62rpx;
      color: #7151ED;
    }

    .btn-right {
      margin-left: 40rpx;
    }
  }
}
</style>
