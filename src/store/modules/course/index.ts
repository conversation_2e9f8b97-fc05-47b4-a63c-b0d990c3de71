import { defineStore } from 'pinia';
import { getCourseDetail } from '@/api/course';
import type { AfterclassQuestion, CourseData, CourseList, CoursePlan, DetailModel, InterdisciplinaryModel, InterdisciplinaryOvestion } from '@/api/course/types';

const useCourseStore = defineStore('course', {
  state: (): CourseList => ({
    id: '',
    name: '',
    sort: 0,
    description: '',
    picture: '',
    trial: false,
    subjectName: '',
    contentDiffuseDetail: null,
    contentDiffuseSummary: null,
    contentDiffuseUrl: '',
    contentPlanDetail: null,
    contentPlanSummary: null,
    contentPlanUrl: '',
    contentUseDetail: null,
    contentUseSummary: null,
    contentUseUrl: '',
    coursePreviewCopy: '',
    contentDiffuseDetailCopy: '',
    contentUseDetailCopy: '',
  }),
  getters: {
    courseInfo(state: CourseList): CourseList {
      return { ...state };
    },
  },
  actions: {
    // 设置课程数据
    setData(partial: Partial<CourseList>) {
      this.$patch(partial);
    },
    // 重置课程数据
    resetData() {
      this.$reset();
    },
    // 获取用户信息
    async getCourseDetail(id: string | number) {
      const res: CourseData = await getCourseDetail({ id });
      // 课后发散
      if (res.contentDiffuseDetail) {
        res.contentDiffuseDetail = JSON.parse(res.contentDiffuseDetail);
        // 课后发散拷贝
        let contentDiffuseDetailCopy = '';
        (res.contentDiffuseDetail as unknown as AfterclassQuestion[]).forEach((item: AfterclassQuestion) => {
          contentDiffuseDetailCopy += `${item.name}\n${item.intro_detail.join('\n')}\n`;
        });
        res.contentDiffuseDetailCopy = contentDiffuseDetailCopy;
      }
      if (res.contentDiffuseSummary)
        res.contentDiffuseSummary = JSON.parse(res.contentDiffuseSummary);
      // 教案
      if (res.contentPlanDetail) {
        res.contentPlanDetail = JSON.parse(res.contentPlanDetail);
        // 课程预告拷贝
        let coursePreviewCopy = '';
        (res.contentPlanDetail as unknown as CoursePlan).course_preview.forEach((item: DetailModel) => {
          coursePreviewCopy += `${item.title}\n${item.description}\n`;
        });
        res.coursePreviewCopy = coursePreviewCopy;
      }
      if (res.contentPlanSummary)
        res.contentPlanSummary = JSON.parse(res.contentPlanSummary);
      // 跨学科应用
      if (res.contentUseDetail) {
        res.contentUseDetail = JSON.parse(res.contentUseDetail);
        // 跨学科应用拷贝
        let contentUseDetailCopy = '';
        (res.contentUseDetail as unknown as InterdisciplinaryModel[]).forEach((item: InterdisciplinaryModel) => {
          contentUseDetailCopy += `${item.topic}\n`;
          item.questions.forEach((question: InterdisciplinaryOvestion) => {
            contentUseDetailCopy += `${question.question_title_type}\n${question.question_title}\n`;
            if (question.question_type === 1) { // 选择题
              contentUseDetailCopy += `${question.options.join('\n')}\n`;
            }
            if (question.question_type === 3) { // 连线题
              question.lef_options.forEach((text: string, index: number) => {
                contentUseDetailCopy += `${text} ${question.right_options[index]}\n`;
              });
            }
            // contentUseDetailCopy += `答案：${question.answer}\n`;
          });
        });
        res.contentUseDetailCopy = contentUseDetailCopy;
      }
      if (res.contentUseSummary)
        res.contentUseSummary = JSON.parse(res.contentUseSummary);
      this.setData(res as unknown as CourseList);
    },
  },
});

export default useCourseStore;
