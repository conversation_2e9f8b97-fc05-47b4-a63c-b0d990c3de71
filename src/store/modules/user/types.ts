export type RoleType = '' | '*' | 'user';
export interface UserState {
  id: string | number; // 用户编号
  nickname: string; // 昵称
  avatar: string; // 头像
  mobile: string; // 手机号
  sex: number; // 性别
  point: number; // 积分
  experience: number; // 经验值
  level: {
    id: string | number; // 等级id
    name: string; // 等级名称
    level: number; // 等级
    icon: string; // 等级图标
  } | undefined;
  brokerageEnabled: boolean; // 是否成为推广员
  levelExpireTime: string; // 会员等级到期时间
  unitCount: number; // 我拥有的单元数量
  courseCount: number; // 我拥有的课程数量
  isLoggedIn: boolean; // 登录状态
}

export type providerType =
  | 'weixin'
  | 'qq'
  | 'sinaweibo'
  | 'xiaomi'
  | 'apple'
  | 'univerify'
  | undefined;
