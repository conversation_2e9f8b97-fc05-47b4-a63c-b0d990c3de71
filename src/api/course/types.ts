import type { MemberInfo } from '../user/types';

export interface StageList {
  id: number; // 阶段id
  name: string; // 阶段名称
  sort?: number; // 排序
  description?: string; // 简介
  unit?: UnitList[];
  memberList?: MemberInfo[]; // 阶段成员列表
}

export interface UnitList {
  id: string | number; // 单元id
  name: string; // 单元名称
  sort: number; // 排序
  description: string; // 简介
  picture: string; // 图片
  trial: boolean; // 是否试看
  course?: CourseData[];
}

export interface CourseData {
  id: string | number; // 课程id
  name: string; // 课程名称
  sort: number; // 排序
  description: string; // 简介
  picture: string; // 图片
  trial: boolean; // 是否试看
  subjectName: string; // 学科名称
  contentDiffuseDetail: string; // 课后发散(明细)
  contentDiffuseSummary: string; // 课后发散(摘要)
  contentDiffuseUrl: string; // 课后发散(文档下载地址)
  contentPlanDetail: string; // 教案内容(明细)
  contentPlanSummary: string; // 教案内容(摘要)
  contentPlanUrl: string; // 教案内容(文档下载地址)
  contentUseDetail: string; // 跨学科应用(明细)
  contentUseSummary: string; // 跨学科应用(摘要)
  contentUseUrl: string; // 跨学科应用(文档下载地址)
  [key: string]: any;
}

export interface CourseList {
  id: string | number; // 课程id
  name: string; // 课程名称
  sort: number; // 排序
  description: string; // 简介
  picture: string; // 图片
  trial: boolean; // 是否试看
  subjectName: string; // 学科名称
  contentDiffuseDetail: AfterclassQuestion[] | null; // 课后发散(明细)
  contentDiffuseSummary: AfterclassQuestion | null; // 课后发散(摘要)
  contentDiffuseUrl: string; // 课后发散(文档下载地址)
  contentPlanDetail: CoursePlan | null; // 教案内容(明细)
  contentPlanSummary: CoursePLanSummary | null; // 教案内容(摘要)
  contentPlanUrl: string; // 教案内容(文档下载地址)
  contentUseDetail: InterdisciplinaryModel[] | null; // 跨学科应用(明细)
  contentUseSummary: InterdisciplinaryModel[] | null; // 跨学科应用(摘要)
  contentUseUrl: string; // 跨学科应用(文档下载地址)
  coursePreviewCopy: string; // 课程预告拷贝内容
  contentDiffuseDetailCopy: string; // 课后发散拷贝内容
  contentUseDetailCopy: string; // 跨学科应用拷贝内容
}

export interface DetailModel {
  title: string; //  标题
  description: string; // 详细内容
}

// 课程教案详情数据
export interface CoursePlan {
  course_preview: DetailModel[]; // 课程预告
  experiment_implementation_aim: string; // 实验实施环节的目标
  experiment_implementation_steps: DetailModel[]; // 实验实施的步骤
  materials_list: DetailModel[]; // 实验材料清单列表
  materials_tip: string; // 实验材料补充说明
  principle_summary_aim: string; // 原理总结的核心目标
  principle_summary_steps: DetailModel[]; // 原理总结的步骤
  suspense_introduction_aim: string; // 悬念导入的核心目标
  suspense_introduction_reason: string; // 悬念导入的环节意图
  suspense_introduction_steps: DetailModel[]; // 悬念导入的步骤
}

// 课程教案总结
export interface CoursePLanSummary {
  course_preview: string; // 课程预告
  materials: string[]; // 实验材料
  suspense_introduction_steps: string[]; // 悬念导入的步骤
  experiment_implementation_steps: string[]; // 实验实施的步骤
  principle_summary_steps: string[]; // 原理总结的步骤
}

// 跨学科应用数据
export interface InterdisciplinaryOvestion {
  question_type: number; // 题目类型 1.选择题 2.问答题 3.连线题
  question_title: string; // 颖目标题
  question_title_type: string; // 题干
  options: string[]; // 选项 选择题时不为空
  lef_options: string[]; // 连线题不为空 连线题的左选项
  right_options: string[]; // 连线题不为空 连线题的右选项
  answer: string; // 答案及解析
}

export interface InterdisciplinaryModel {
  topic: string; // 跨学科应用的主题
  questions: InterdisciplinaryOvestion[];
}

// 课后发散数据模型
export interface AfterclassQuestion {
  type: number; // 1课后发散 2课后反馈
  name: string; // 实验名称
  intro_detail: string[];// 实验介绍
}
