/**
 * 课程相关接口
 */
import type { CourseData, StageList, UnitList } from './types';
import { get } from '@/utils/request';

enum URL {
  getStage = '/edu/stage/page',
  getStageDetail = '/edu/stage/get',
  getUnit = '/edu/unit/page',
  getUnitDetail = '/edu/unit/get',
  getCourse = '/edu/course/page',
  getCourseDetail = '/edu/course/get',
}

// 获取阶段列表
export const getStage = () => get<{ total: number; list: StageList[] } | 'update'>({ url: URL.getStage });

// 获取阶段详情
export const getStageDetail = (data: { id: string | number }) => get<StageList>({ url: URL.getStageDetail, data });

// 获取单元列表
export const getUnit = (data: { stageId: string | number }) => get<{ total: number; list: UnitList[] }>({ url: URL.getUnit, data });

// 获取单元详情
export const getUnitDetail = (data: { id: string | number }) => get<UnitList>({ url: URL.getUnitDetail, data });

// 获取课程列表
export const getCourse = (data: { unitId: string | number }) => get<{ total: number; list: CourseData[] }>({ url: URL.getCourse, data });

// 获取课程详情
export const getCourseDetail = (data: { id: string | number }) => get<CourseData>({ url: URL.getCourseDetail, data });
