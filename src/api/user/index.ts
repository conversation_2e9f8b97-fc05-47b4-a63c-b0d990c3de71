/**
 * 用户信息相关接口
 */
import type { CodeInfo, CourseInfo, LoginParams, LoginResult, MemberInfo } from './types';
import { del, get, post, put } from '@/utils/request';
import type { UserState } from '@/store/modules/user/types';

enum URL {
  login = '/member/auth/weixin-mini-app-login',
  logout = '/member/auth/logout',
  profile = '/member/user/get',
  sendCode = '/member/auth/send-sms-code',
  refreshToken = '/member/auth/refresh-token',
  updateInfo = '/member/user/update',
  myCourses = '/member/user/my-courses',
  getMembers = '/member/team/members',
  getMemberCount = '/member/team/member-count',
  remove = '/member/team/members/',
  removeBatch = '/member/team/members/batch-remove',
  invite = '/member/team/invite',
  accept = '/member/invitation/accept',
  getCodeInfo = '/member/app/exchange-code/info',
  exchangeCode = '/member/app/exchange-code/exchange',
}

export const getUserProfile = () => get<UserState>({ url: URL.profile });
export const login = (data: LoginParams) => post<LoginResult>({ url: URL.login, data });
export const logout = () => post<any>({ url: URL.logout });

export const sendCode = (data: { mobile: string; scene: number }) => post<any>({ url: URL.sendCode, data });

export const refreshToken = (data: { refreshToken: string }) => post<any>({ url: URL.refreshToken, params: data });

export const updateInfo = (data: { nickname: string; avatar: string }) => put<any>({ url: URL.updateInfo, data });

export const myCourses = () => get<{ stages: CourseInfo[] }>({ url: URL.myCourses });

// 获取团队成员
export const getMembers = (data?: { stageId: number }) => get<Array<MemberInfo>>({ url: URL.getMembers, data });

// 获取团队成员数量 阶段ID（可选，不传则返回所有阶段总数） 是否去重统计（true=去重统计总人数，false=按阶段统计）
export const getMemberCount = (data?: { stageId?: number; distinct?: boolean }) => get<number>({ url: URL.getMemberCount, data });

// 移除成员
export const removeMember = (id: string) => del<any>({ url: URL.remove + id });
// 批量删除成员
export const removeMemberBatch = (data: { memberIds: number[] }) => post<any>({ url: URL.removeBatch, data });

// 邀请成员
export const invite = (data: { stageIds: number[] }) => post<{ invitationCode: string }>({ url: URL.invite, data });

// 接受邀请
export const accept = (data: { invitationCode: string }) => post<any>({ url: URL.accept, data });

// 获取兑换码信息
export const getCodeInfo = (data: { exchangeCode: string }) => get<CodeInfo>({ url: URL.getCodeInfo, data });

// 兑换兑换码
export const exchangeCode = (data: { exchangeCode: string; selectedStageIds?: number[] }) => post<any>({ url: URL.exchangeCode, data });
