export interface LoginParams {
  phoneCode: string;
  loginCode: string;
  state: string;
}

export interface LoginByCodeParams {
  code: string;
}

export interface LoginResult {
  token: string;
  user_id: number;
  user_name: string;
  avatar: string;
}

export interface CourseInfo {
  id: number;
  name: string;
  description: string;
  sort: number;
  courseCount: number; // 我拥有的课程数量
  owned: boolean; // 是否拥有该阶段
}

export interface MemberInfo {
  memberId: number; // 用户编号
  memberNickname: string; // 昵称
  memberAvatar: string; // 头像
  memberMobile: string; // 手机号
  stageId: number; // 阶段ID
  stageName: string; // 阶段名称
  stageIds: number[]; // 用户所在的所有阶段ID列表
  stageNames: string[]; // 用户所在的所有阶段名称列表
  joinTime: string; // 加入时间
  status: number; // 状态
  adminMember: number; // 是否是管理员
}

export interface InviteItem {
  stageId: number;
}

export interface CodeInfo {
  levelId: number; // 会员等级编号
  levelName: string; // 会员等级名称
  price: number; // 价格
  duration: number; // 时长
  durationType: number; // 时长类型(0永久1天2月3年)
  stageCount: number; // 会员等级可选择的阶段数量
  totalStageCount: number; // 总阶段数量
  needSelectStage: boolean; // 是否需要弹窗选择阶段
}

export interface JoinNotificationsResult {
  applicationMessages: string[]; // 申请消息列表
  limitMessage: string; // 团队限额提示信息
  currentMemberCount: number; // 当前团队成员数量
  maxMemberCount: number; // 最大团队成员数量
  hasNotifications: boolean; // 是否有新的申请通知

}
