<template>
  <up-popup :show="show" mode="center" :close-on-click-overlay="false" @close="close" @open="open">
    <view class="confirm-box">
      <view class="confirm-info">
        {{ title }}
      </view>
      <view class="confirm-btn" @click="handleConfirm">
        {{ btnText }}
      </view>
    </view>
    <image class="close-btn" src="https://aireader-1302868088.cos.ap-nanjing.myqcloud.com/mini_icon/icon_close.png" @click="close" />
  </up-popup>
</template>

<script setup lang="ts">
defineOptions({
  options: {
    styleIsolation: 'shared',
  },
});

defineProps({
  show: {
    type: Boolean,
    default: false,
  },

  title: {
    type: String,
    default: '',
  },

  btnText: {
    type: String,
    default: '确认',
  },
});

const emit = defineEmits(['update:show', 'handleConfirm']);

const open = () => {
  emit('update:show', true);
};
const close = () => {
  emit('update:show', false);
};
const handleConfirm = () => {
  emit('handleConfirm');
};
</script>

<style lang="scss" scoped>
.confirm-box {
  width: 590rpx;
  padding: 80rpx;
  background: #fff;
  border-radius: 40rpx;

  display: flex;
  flex-direction: column;
  align-items: center;

  .confirm-title {
    width: 100%;
    display: flex;
    align-items: center;

    .user-img {
      width: 56rpx;
      height: 56rpx;
      border-radius: 50%;
      background: #D9D9D9;
    }

    .user-name {
      padding: 0 16rpx;
      font-weight: bold;
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }

    .user-info {
      font-size: 32rpx;
      color: #303030;
      line-height: 48rpx;
    }
  }

  .confirm-info {
    padding: 24rpx 0;
    font-size: 32rpx;
    color: #303030;
    line-height: 72rpx;
  }

  .confirm-btn {
    margin-top: 56rpx;
    width: 343rpx;
    height: 80rpx;
    background: #7151ED;
    border-radius: 16rpx;

    font-size: 32rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 80rpx;
  }
}

.close-btn {
  margin-top: 80rpx;
  width: 88rpx;
  height: 88rpx;
}
</style>

<style lang="scss">
confirm-box {
  .u-popup__content {
    width: 750rpx;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: transparent !important;
  }
}
</style>
