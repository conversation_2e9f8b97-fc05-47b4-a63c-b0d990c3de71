<template>
  <view class="navigation-bar-view text-center" :style="{ 'padding-top': `${statusBarHeight * 2}rpx`, 'line-height': `${navigatorHeight * 2}rpx` }">
    {{ navigationTitle }}
    <view v-if="showBack" class="back-view" :style="{ top: `${statusBarHeight * 2 + (navigatorHeight - (isLandscape ? 18 * hDivideW : 18))}rpx`, left: isLandscape ? '30rpx' : '30rpx' }" @click="backClick()">
      <up-icon name="arrow-left" :size="18" color="" />
    </view>
  </view>
</template>

<script setup lang="ts" name="NavigationBar">
import storage from '@/utils/storage';

const props = defineProps({
  navigationTitle: {
    type: String,
    default: '',
  },
  showBack: {
    type: Boolean,
    default: true,
  },
  goBack: {
    type: [Function, Number],
    default: 0,
  },

  isLandscape: {
    type: <PERSON><PERSON>an,
    default: false,
  },
});

const backClick = () => {
  if (typeof props.goBack === 'function') {
    props.goBack();
  }
  else {
    uni.navigateBack();
  }
};
const statusBarHeight = ref<number>(40); // 状态栏的高度
const navigatorHeight = ref<number>(40); // 导航栏高度
const menuHeight = ref<number>(0); // 胶囊高度
const menuTop = ref<number>(0); // 胶囊与顶部的距离
// const totalHeight = ref<number>(0); // 总高度
const hDivideW = ref<number>(0);
onLoad(() => {
  uni.getSystemInfo({
    success: (res: any) => {
      hDivideW.value = res.screenHeight / res.screenWidth;
      if (props.isLandscape) {
        statusBarHeight.value = res.statusBarHeight * hDivideW.value;
      }
      else {
        statusBarHeight.value = res.statusBarHeight || 40;
      }
    },
  });

  const menu: any = uni.getMenuButtonBoundingClientRect();
  menuHeight.value = menu.height; // 胶囊高度
  menuTop.value = menu.top; // 胶囊与顶部的距离
  // 导航栏高度= （胶囊顶部距离-状态栏高度） x 2 + 胶囊的高度
  const navigatorH = (menu.top - statusBarHeight.value) * 2 + menu.height;
  // 总高度 = 状态栏的高度 + 导航栏高度
  // const totalH = statusBarHeight.value + navigatorHeight.value;

  if (props.isLandscape) {
    navigatorHeight.value = navigatorH * hDivideW.value;
    // totalHeight.value = totalH * hDivideW.value;
  }
  else {
    navigatorHeight.value = navigatorH <= 30 ? 40 : navigatorH;
    // totalHeight.value = totalH;
  }

  // 存储导航栏总高度
  const totalNavigationHeight = (navigatorHeight.value + statusBarHeight.value) * 2;
  storage.set('navigation-total-height', totalNavigationHeight.toString());
});
</script>

<style lang="scss">
.navigation-bar-view {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  font-size: 32rpx;
  color: #fff;
  z-index: 99;

  .back-view {
    position: absolute;
    // left: 30rpx;
  }
}
</style>
