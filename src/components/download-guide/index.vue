<template>
  <view class="download-guide" @click="close">
    <image v-if="isIos" class="guide-img img-ios" src="https://cbandu.oss-cn-qingdao.aliyuncs.com/imgs/20250428/img_ios.png" mode="aspectFill" />
    <image v-else class="guide-img" src="https://cbandu.oss-cn-qingdao.aliyuncs.com/imgs/20250428/img_android.png" mode="aspectFill" />

    <image class="arrow-img" src="https://yshubook.oss-cn-qingdao.aliyuncs.com/profile/upload/20250417/img_arrow.png" mode="aspectFill" />

    <view class="preview-btn" @click.stop="openPreview">
      打开预览
    </view>
  </view>
</template>

<script setup lang="ts">
const emit = defineEmits(['openPreview', 'close']);

const system = uni.getSystemInfoSync().system.toLowerCase();
const isIos = ref(system.includes('ios'));

const openPreview = () => {
  emit('openPreview');
};

const close = () => {
  emit('close');
};
</script>

<style lang="scss" scoped>
.download-guide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;

  .guide-img {
    width: 590rpx;
    height: 769rpx;
    margin-top: 100rpx;
  }

  .img-ios {
    height: 998rpx;
  }

  .arrow-img {
    position: absolute;
    top: 25rpx;
    right: 75rpx;
    width: 288rpx;
    height: 369rpx;
  }

  .preview-btn {
    margin-bottom: 40rpx;
    padding: 0 48rpx;
    height: 64rpx;
    border-radius: 32rpx;
    border: 2rpx solid #fff;

    font-size: 28rpx;
    line-height: 60rpx;
    color: #fff;
  }
}
</style>
