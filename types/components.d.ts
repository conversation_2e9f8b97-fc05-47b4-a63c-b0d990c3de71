/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AgreePrivacy: typeof import('./../src/components/agree-privacy/index.vue')['default']
    ConfirmBox: typeof import('./../src/components/confirm-box/index.vue')['default']
    DownloadGuide: typeof import('./../src/components/download-guide/index.vue')['default']
    NavigationBar: typeof import('./../src/components/navigation-bar/index.vue')['default']
    PageNav: typeof import('./../src/components/page-nav/page-nav.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBar: typeof import('./../src/components/tab-bar/index.vue')['default']
    Upload: typeof import('./../src/components/upload/index.vue')['default']
  }
}
